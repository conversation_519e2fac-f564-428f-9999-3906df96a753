"""
图像提示词构建器

负责将用户输入和角色设定转换为图像生成提示词
"""

from typing import Dict, List, Optional, Any, Tuple

from .models import CharacterBasicInfo, AppearanceParams, ImagePromptResult


class ImagePromptBuilder:
    """图像提示词构建器，用于生成和优化图像提示词"""
    
    def __init__(self, template_loader):
        """
        初始化图像提示词构建器
        
        Args:
            template_loader: 提供提示词模板的加载器
        """
        self.template_loader = template_loader
        self.image_templates = self.template_loader.get_template("image_templates")
        self.MAX_PROMPT_LENGTH = 1000  # 星火API提示词长度限制
        
    def build_initial_prompt(
        self, 
        character_info: CharacterBasicInfo,
        appearance_params: Optional[AppearanceParams] = None
    ) -> ImagePromptResult:
        """
        构建初始角色生成提示词
        
        Args:
            character_info: 角色基本信息
            appearance_params: 角色外观参数(可选)
            
        Returns:
            ImagePromptResult: 包含正面和负面提示词的结果对象
        """
        prompt_parts = []
        truncated_parts = []
        is_truncated = False
        
        # 添加基础人物描述
        if character_info.race:
            race_prompt = self._get_race_prompt(character_info.race)
            prompt_parts.append(race_prompt)
            
        # 添加基于动漫参考的描述
        if character_info.anime_reference:
            anime_prompt = f"character from {character_info.anime_reference}, anime style"
            prompt_parts.append(anime_prompt)
            
        # 添加年龄相关描述
        if character_info.age is not None:
            age_prompt = self._get_age_prompt(character_info.age)
            prompt_parts.append(age_prompt)
            
        # 添加身份相关描述
        if character_info.identity:
            prompt_parts.append(character_info.identity)
            
        # 添加性格相关描述
        if character_info.personality:
            personality_prompt = ", ".join(character_info.personality)
            prompt_parts.append(personality_prompt)
            
        # 如果有外观参数，添加对应的描述
        if appearance_params:
            appearance_prompt_parts = self._build_appearance_prompt(appearance_params)
            prompt_parts.extend(appearance_prompt_parts)
            
        # 添加质量修饰词
        quality_modifiers = self.image_templates.get("modifiers", {}).get("quality", [])
        style_modifiers = self.image_templates.get("modifiers", {}).get("style", [])
        
        prompt_parts.extend(quality_modifiers)
        prompt_parts.append(style_modifiers[0] if style_modifiers else "anime style")
        
        # 构建完整的正面提示词，确保不超过长度限制
        positive_prompt, truncated, is_truncated = self._ensure_length_limit(prompt_parts)
        
        if is_truncated:
            truncated_parts = truncated
            
        # 构建负面提示词
        negative_prompt = ", ".join(self.image_templates.get("negative_prompts", []))
        
        return ImagePromptResult(
            positive_prompt=positive_prompt,
            negative_prompt=negative_prompt,
            is_truncated=is_truncated,
            truncated_content=truncated_parts
        )
        
    def _get_race_prompt(self, race: str) -> str:
        """获取种族相关提示词"""
        race_templates = self.image_templates.get("categories", {}).get("race", {})
        return race_templates.get(race.lower(), race)
    
    def _get_age_prompt(self, age: int) -> str:
        """根据年龄获取相关描述"""
        if age < 10:
            return "young child"
        elif age < 14:
            return "child"
        elif age < 18:
            return "teenager"
        elif age < 25:
            return "young adult"
        elif age < 40:
            return "adult"
        elif age < 60:
            return "middle-aged"
        else:
            return "elderly"
            
    def _build_appearance_prompt(self, appearance_params: AppearanceParams) -> List[str]:
        """构建外观参数的提示词"""
        appearance_parts = []
        
        # 处理发型
        if appearance_params.hair_style:
            hair_style_templates = self.image_templates.get("categories", {}).get("hair_style", {})
            hair_style = hair_style_templates.get(appearance_params.hair_style.lower(), appearance_params.hair_style)
            appearance_parts.append(hair_style)
            
        # 处理发色
        if appearance_params.hair_color:
            hair_color_templates = self.image_templates.get("categories", {}).get("hair_color", {})
            hair_color = hair_color_templates.get(appearance_params.hair_color.lower(), appearance_params.hair_color)
            appearance_parts.append(hair_color)
            
        # 处理瞳色
        if appearance_params.eye_color:
            appearance_parts.append(f"{appearance_params.eye_color} eyes")
            
        # 处理肤色
        if appearance_params.skin_tone:
            appearance_parts.append(f"{appearance_params.skin_tone} skin")
            
        # 处理体型
        if appearance_params.body_type:
            appearance_parts.append(appearance_params.body_type)
            
        # 处理服装风格
        if appearance_params.outfit_style:
            appearance_parts.append(f"wearing {appearance_params.outfit_style}")
            
        # 处理表情
        if appearance_params.facial_expression:
            appearance_parts.append(appearance_params.facial_expression)
            
        # 处理额外参数
        for key, value in appearance_params.additional_params.items():
            if isinstance(value, str) and value:
                appearance_parts.append(f"{key}: {value}")
                
        return appearance_parts
        
    def _ensure_length_limit(self, prompt_parts: List[str]) -> Tuple[str, List[str], bool]:
        """
        确保提示词不超过长度限制
        
        Args:
            prompt_parts: 提示词部分列表
            
        Returns:
            Tuple[str, List[str], bool]: (处理后的提示词, 被截断的部分, 是否被截断)
        """
        # 优先级：质量词 > 基本特征 > 细节描述 > 风格词
        # 先对提示词部分进行优先级排序
        priority_mapping = {
            "((best quality))": 100,
            "((masterpiece))": 99,
            "((highly detailed))": 98,
            "((beautiful))": 97,
            "((ultra-detailed))": 96,
            "anime style": 50,
            "japanese anime": 49,
            "detailed anime": 48,
        }
        
        # 按优先级排序，优先级未明确的项保持原顺序
        sorted_parts = sorted(
            enumerate(prompt_parts),
            key=lambda x: priority_mapping.get(x[1], 90 - x[0]),
            reverse=True
        )
        sorted_prompt_parts = [part for _, part in sorted_parts]
        
        # 开始组合提示词，确保不超过长度限制
        final_parts = []
        truncated_parts = []
        current_length = 0
        is_truncated = False
        
        for part in sorted_prompt_parts:
            part_length = len(part) + 2  # +2 for separator ", "
            if current_length + part_length <= self.MAX_PROMPT_LENGTH:
                final_parts.append(part)
                current_length += part_length
            else:
                truncated_parts.append(part)
                is_truncated = True
                
        # 组合最终的提示词
        return ", ".join(final_parts), truncated_parts, is_truncated
        
    def update_prompt_with_custom_description(
        self,
        current_prompt: str,
        custom_description: str
    ) -> ImagePromptResult:
        """
        使用自定义描述更新提示词
        
        Args:
            current_prompt: 当前的提示词
            custom_description: 自定义描述
            
        Returns:
            ImagePromptResult: 更新后的提示词结果
        """
        # 简单实现：将自定义描述添加到提示词的开头
        parts = [custom_description]
        if current_prompt:
            parts.append(current_prompt)
            
        full_prompt, truncated, is_truncated = self._ensure_length_limit(parts)
        negative_prompt = ", ".join(self.image_templates.get("negative_prompts", []))
        
        return ImagePromptResult(
            positive_prompt=full_prompt,
            negative_prompt=negative_prompt,
            is_truncated=is_truncated,
            truncated_content=truncated
        ) 