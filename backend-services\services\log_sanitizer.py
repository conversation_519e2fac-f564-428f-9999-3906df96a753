"""
日志敏感数据保护模块 - 为虚拟角色平台提供日志中敏感信息脱敏功能
"""
import re
import logging
import json
from functools import wraps
from copy import deepcopy


class SensitiveDataFilter(logging.Filter):
    """
    日志敏感数据过滤器，用于脱敏日志中的敏感信息
    """
    # 默认敏感字段名称
    DEFAULT_SENSITIVE_FIELDS = [
        'password', 'token', 'secret', 'auth', 'key', 'credential', 
        'api_key', 'access_key', 'secret_key', 'private_key',
        'card', 'credit', 'phone', 'mobile', 'email', 'address',
        'id_card', 'identity', 'birth', 'birthday'
    ]
    
    # 正则表达式模式
    PATTERNS = {
        # 手机号码 (中国大陆)
        'phone': re.compile(r'1[3-9]\d{9}'),
        
        # 邮箱地址
        'email': re.compile(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'),
        
        # 身份证号码 (18位)
        'id_card': re.compile(r'\d{17}[\dXx]'),
        
        # 信用卡号
        'credit_card': re.compile(r'\d{4}[ -]?\d{4}[ -]?\d{4}[ -]?\d{4}'),
        
        # API密钥 (通用格式)
        'api_key': re.compile(r'[a-zA-Z0-9]{32,}'),
        
        # OAuth令牌
        'oauth_token': re.compile(r'(access_token|refresh_token)["\':\s=]+[a-zA-Z0-9._-]+'),
        
        # JWT令牌
        'jwt': re.compile(r'eyJ[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+'),
        
        # 密码字段
        'password': re.compile(r'(password|passwd)["\':\s=]+[^\s,;]{3,}'),
    }
    
    def __init__(self, sensitive_fields=None, mask_char='*', mask_length=4):
        """
        初始化过滤器
        
        Args:
            sensitive_fields: 敏感字段名称列表，如果为None则使用默认列表
            mask_char: 用于替换敏感信息的字符
            mask_length: 掩码长度
        """
        self.sensitive_fields = sensitive_fields or self.DEFAULT_SENSITIVE_FIELDS
        self.mask_char = mask_char
        self.mask_length = mask_length
    
    def filter(self, record):
        """
        过滤日志记录，脱敏敏感信息
        
        Args:
            record: 日志记录
            
        Returns:
            过滤后的日志记录
        """
        # 脱敏消息文本
        if isinstance(record.msg, str):
            record.msg = self._sanitize_text(record.msg)
        
        # 脱敏异常信息
        if record.exc_info and record.exc_info[1]:
            # 不能直接修改异常对象，但可以修改其字符串表示
            exc_str = str(record.exc_info[1])
            sanitized_exc_str = self._sanitize_text(exc_str)
            if exc_str != sanitized_exc_str:
                # 如果发生了脱敏，记录一下
                record.exc_text = f"原始异常信息已脱敏: {sanitized_exc_str}"
        
        # 脱敏额外字段
        if hasattr(record, 'extra') and isinstance(record.extra, dict):
            record.extra = self._sanitize_dict(record.extra)
        
        # 脱敏args
        if record.args:
            record.args = self._sanitize_args(record.args)
        
        return True
    
    def _sanitize_text(self, text):
        """
        脱敏文本中的敏感信息
        
        Args:
            text: 需要脱敏的文本
            
        Returns:
            脱敏后的文本
        """
        if not isinstance(text, str):
            return text
        
        result = text
        
        # 使用正则表达式匹配并替换敏感信息
        for pattern_name, pattern in self.PATTERNS.items():
            result = pattern.sub(self._mask_match, result)
        
        # 检查JSON字符串并脱敏
        if result.strip().startswith('{') and result.strip().endswith('}'):
            try:
                data = json.loads(result)
                if isinstance(data, dict):
                    sanitized_data = self._sanitize_dict(data)
                    if data != sanitized_data:
                        result = json.dumps(sanitized_data)
            except (json.JSONDecodeError, TypeError):
                pass
        
        return result
    
    def _sanitize_dict(self, data):
        """
        脱敏字典中的敏感信息
        
        Args:
            data: 需要脱敏的字典
            
        Returns:
            脱敏后的字典
        """
        if not isinstance(data, dict):
            return data
        
        result = deepcopy(data)
        
        # 遍历字典的键值对
        for key, value in data.items():
            # 检查键名是否包含敏感字段
            if any(sensitive_field.lower() in key.lower() for sensitive_field in self.sensitive_fields):
                if isinstance(value, str):
                    result[key] = self._mask_value(value)
                elif isinstance(value, (int, float)):
                    result[key] = self._mask_value(str(value))
            # 递归处理嵌套字典
            elif isinstance(value, dict):
                result[key] = self._sanitize_dict(value)
            # 递归处理列表
            elif isinstance(value, list):
                result[key] = [
                    self._sanitize_dict(item) if isinstance(item, dict) 
                    else (self._sanitize_text(item) if isinstance(item, str) else item)
                    for item in value
                ]
            # 处理字符串值
            elif isinstance(value, str):
                result[key] = self._sanitize_text(value)
        
        return result
    
    def _sanitize_args(self, args):
        """
        脱敏参数中的敏感信息
        
        Args:
            args: 需要脱敏的参数
            
        Returns:
            脱敏后的参数
        """
        if isinstance(args, dict):
            return self._sanitize_dict(args)
        elif isinstance(args, (list, tuple)):
            return tuple(
                self._sanitize_dict(arg) if isinstance(arg, dict)
                else (self._sanitize_text(arg) if isinstance(arg, str) else arg)
                for arg in args
            )
        elif isinstance(args, str):
            return self._sanitize_text(args)
        return args
    
    def _mask_match(self, match):
        """
        对正则表达式匹配到的内容进行掩码
        
        Args:
            match: 正则表达式匹配对象
            
        Returns:
            掩码后的字符串
        """
        value = match.group(0)
        return self._mask_value(value)
    
    def _mask_value(self, value):
        """
        对敏感值进行掩码
        
        Args:
            value: 需要掩码的值
            
        Returns:
            掩码后的值
        """
        if not value:
            return value
        
        # 根据值的长度决定保留的字符数
        if len(value) <= 4:
            # 短值完全掩码
            return self.mask_char * len(value)
        elif len(value) <= 8:
            # 中等长度值保留前1后1
            return value[0] + self.mask_char * (len(value) - 2) + value[-1]
        else:
            # 长值保留前2后2
            return value[:2] + self.mask_char * (len(value) - 4) + value[-2:]


def sanitize_log_message(func):
    """
    装饰器，用于脱敏日志消息
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    sanitizer = SensitiveDataFilter()
    
    @wraps(func)
    def wrapper(self, msg, *args, **kwargs):
        # 脱敏消息
        sanitized_msg = sanitizer._sanitize_text(msg)
        
        # 脱敏参数
        sanitized_args = sanitizer._sanitize_args(args)
        
        # 调用原始函数
        return func(self, sanitized_msg, *sanitized_args, **kwargs)
    
    return wrapper


def apply_sanitization_to_logger(logger):
    """
    为logger应用敏感信息脱敏
    
    Args:
        logger: 需要应用脱敏的logger实例
        
    Returns:
        应用了脱敏的logger实例
    """
    # 添加敏感数据过滤器
    logger.addFilter(SensitiveDataFilter())
    
    # 装饰logger的方法
    original_debug = logger.debug
    original_info = logger.info
    original_warning = logger.warning
    original_error = logger.error
    original_critical = logger.critical
    
    logger.debug = sanitize_log_message(original_debug)
    logger.info = sanitize_log_message(original_info)
    logger.warning = sanitize_log_message(original_warning)
    logger.error = sanitize_log_message(original_error)
    logger.critical = sanitize_log_message(original_critical)
    
    return logger


def configure_sanitized_logging():
    """
    配置带有敏感信息脱敏的日志系统
    """
    # 获取根logger
    root_logger = logging.getLogger()
    
    # 应用敏感信息脱敏
    apply_sanitization_to_logger(root_logger)
    
    # 为常用的logger应用脱敏
    for logger_name in ['django', 'django.request', 'app']:
        logger = logging.getLogger(logger_name)
        apply_sanitization_to_logger(logger)
    
    return root_logger 