import logging
import time
import threading
import schedule
from datetime import datetime
from .file_cleanup_service import FileCleanupService

logger = logging.getLogger(__name__)

class ScheduledTaskManager:
    """
    定时任务管理器，用于管理和执行各种定时任务
    """
    
    def __init__(self):
        """初始化定时任务管理器"""
        self.running = False
        self.scheduler_thread = None
        self.file_cleanup_service = FileCleanupService()
        
    def start(self):
        """启动定时任务管理器"""
        if self.running:
            logger.warning("定时任务管理器已经在运行")
            return False
            
        # 设置定时任务
        self._setup_tasks()
        
        # 启动调度器线程
        self.running = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler)
        self.scheduler_thread.daemon = True  # 设置为守护线程，使其随主线程一起退出
        self.scheduler_thread.start()
        
        logger.info("定时任务管理器已启动")
        return True
        
    def stop(self):
        """停止定时任务管理器"""
        if not self.running:
            logger.warning("定时任务管理器未在运行")
            return False
            
        self.running = False
        if self.scheduler_thread:
            # 等待调度器线程完成
            self.scheduler_thread.join(timeout=5)
        
        # 清除所有定时任务
        schedule.clear()
        
        logger.info("定时任务管理器已停止")
        return True
        
    def _setup_tasks(self):
        """设置定时任务"""
        # 每天凌晨2点执行文件清理任务
        schedule.every().day.at("02:00").do(self._task_cleanup_files)
        
        # 可以添加更多定时任务
        # schedule.every().hour.do(other_task)
        # schedule.every().monday.at("12:00").do(weekly_task)
        
        logger.info("已设置定时任务")
        
    def _run_scheduler(self):
        """运行调度器循环"""
        while self.running:
            try:
                # 执行所有计划任务
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                logger.error(f"定时任务执行过程中出错: {str(e)}")
                # 继续运行，防止整个调度器因一个错误而崩溃
                time.sleep(300)  # 出错后等待5分钟再继续
                
    def _task_cleanup_files(self):
        """文件清理任务"""
        logger.info("开始执行文件清理任务")
        try:
            # 默认清理30天前软删除的角色图片
            success, failure = self.file_cleanup_service.cleanup_deleted_character_images(retention_days=30)
            logger.info(f"文件清理任务完成，成功: {success}, 失败: {failure}")
        except Exception as e:
            logger.error(f"文件清理任务出错: {str(e)}")
            
        return True  # 返回True以便任务仍然保留在计划中
        
# 全局单例实例，可在应用启动时初始化
task_manager = None 