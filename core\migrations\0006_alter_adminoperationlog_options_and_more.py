# Generated by Django 5.2.1 on 2025-07-02 12:06

import core.models
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0005_change_image_url_to_charfield'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='adminoperationlog',
            options={'verbose_name': '管理员操作日志', 'verbose_name_plural': '管理员操作日志'},
        ),
        migrations.AlterModelOptions(
            name='adminrole',
            options={'verbose_name': '管理员角色', 'verbose_name_plural': '管理员角色'},
        ),
        migrations.AlterModelOptions(
            name='character',
            options={'ordering': ['created_at'], 'verbose_name': '虚拟角色', 'verbose_name_plural': '虚拟角色'},
        ),
        migrations.AlterModelOptions(
            name='characterprompttemplate',
            options={'verbose_name': '角色提示词模板', 'verbose_name_plural': '角色提示词模板'},
        ),
        migrations.AlterModelOptions(
            name='chatmessage',
            options={'ordering': ['sent_at'], 'verbose_name': '聊天消息', 'verbose_name_plural': '聊天消息'},
        ),
        migrations.AlterModelOptions(
            name='prompttemplate',
            options={'verbose_name': '提示词模板', 'verbose_name_plural': '提示词模板'},
        ),
        migrations.AlterModelOptions(
            name='systemconfig',
            options={'verbose_name': '系统配置', 'verbose_name_plural': '系统配置'},
        ),
        migrations.AlterModelOptions(
            name='useradminrole',
            options={'verbose_name': '用户管理员角色', 'verbose_name_plural': '用户管理员角色'},
        ),
        migrations.AlterModelOptions(
            name='userprofile',
            options={'verbose_name': '用户资料', 'verbose_name_plural': '用户资料'},
        ),
        migrations.AddField(
            model_name='character',
            name='gender',
            field=models.CharField(default='女性', max_length=50, verbose_name='性别'),
        ),
        migrations.AlterField(
            model_name='adminoperationlog',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='操作时间'),
        ),
        migrations.AlterField(
            model_name='adminoperationlog',
            name='details',
            field=models.JSONField(blank=True, default=dict, null=True, verbose_name='操作详情'),
        ),
        migrations.AlterField(
            model_name='adminoperationlog',
            name='ip_address',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='IP地址'),
        ),
        migrations.AlterField(
            model_name='adminoperationlog',
            name='operation_type',
            field=models.CharField(choices=[('create', '创建'), ('update', '更新'), ('delete', '删除'), ('login', '登录'), ('logout', '登出'), ('other', '其他')], max_length=50, verbose_name='操作类型'),
        ),
        migrations.AlterField(
            model_name='adminoperationlog',
            name='target_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='目标ID'),
        ),
        migrations.AlterField(
            model_name='adminoperationlog',
            name='target_type',
            field=models.CharField(blank=True, choices=[('character', '角色'), ('prompt_template', '提示词模板'), ('user', '用户'), ('system_config', '系统配置'), ('other', '其他')], max_length=50, null=True, verbose_name='目标类型'),
        ),
        migrations.AlterField(
            model_name='adminoperationlog',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='admin_logs', to=settings.AUTH_USER_MODEL, verbose_name='操作用户'),
        ),
        migrations.AlterField(
            model_name='adminoperationlog',
            name='user_agent',
            field=models.TextField(blank=True, null=True, verbose_name='用户代理'),
        ),
        migrations.AlterField(
            model_name='adminrole',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='创建时间'),
        ),
        migrations.AlterField(
            model_name='adminrole',
            name='description',
            field=models.TextField(blank=True, null=True, verbose_name='角色描述'),
        ),
        migrations.AlterField(
            model_name='adminrole',
            name='name',
            field=models.CharField(max_length=50, unique=True, verbose_name='角色名称'),
        ),
        migrations.AlterField(
            model_name='adminrole',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='更新时间'),
        ),
        migrations.AlterField(
            model_name='character',
            name='age',
            field=models.IntegerField(verbose_name='年龄'),
        ),
        migrations.AlterField(
            model_name='character',
            name='appearance_params',
            field=models.JSONField(blank=True, default=dict, null=True, verbose_name='外观参数'),
        ),
        migrations.AlterField(
            model_name='character',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='创建时间'),
        ),
        migrations.AlterField(
            model_name='character',
            name='deleted_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AlterField(
            model_name='character',
            name='identity',
            field=models.CharField(max_length=255, validators=[core.models.validate_identity], verbose_name='身份'),
        ),
        migrations.AlterField(
            model_name='character',
            name='image_url',
            field=models.CharField(blank=True, max_length=500, null=True, verbose_name='头像URL'),
        ),
        migrations.AlterField(
            model_name='character',
            name='is_deleted',
            field=models.BooleanField(default=False, verbose_name='是否删除'),
        ),
        migrations.AlterField(
            model_name='character',
            name='name',
            field=models.CharField(max_length=255, verbose_name='角色名称'),
        ),
        migrations.AlterField(
            model_name='character',
            name='personality',
            field=models.CharField(max_length=255, validators=[core.models.validate_personality], verbose_name='性格'),
        ),
        migrations.AlterField(
            model_name='character',
            name='public',
            field=models.BooleanField(default=False, verbose_name='是否公开'),
        ),
        migrations.AlterField(
            model_name='character',
            name='settings',
            field=models.JSONField(blank=True, default=dict, null=True, verbose_name='系统设置'),
        ),
        migrations.AlterField(
            model_name='character',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='更新时间'),
        ),
        migrations.AlterField(
            model_name='character',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='characters', to=settings.AUTH_USER_MODEL, verbose_name='创建用户'),
        ),
        migrations.AlterField(
            model_name='characterprompttemplate',
            name='character',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='prompt_templates', to='core.character', verbose_name='角色'),
        ),
        migrations.AlterField(
            model_name='characterprompttemplate',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='创建时间'),
        ),
        migrations.AlterField(
            model_name='characterprompttemplate',
            name='custom_content',
            field=models.TextField(blank=True, null=True, verbose_name='自定义内容'),
        ),
        migrations.AlterField(
            model_name='characterprompttemplate',
            name='template',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='characters', to='core.prompttemplate', verbose_name='提示词模板'),
        ),
        migrations.AlterField(
            model_name='characterprompttemplate',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='更新时间'),
        ),
        migrations.AlterField(
            model_name='characterprompttemplate',
            name='variables_values',
            field=models.JSONField(blank=True, default=dict, null=True, verbose_name='变量值'),
        ),
        migrations.AlterField(
            model_name='chatmessage',
            name='character',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='core.character', verbose_name='角色'),
        ),
        migrations.AlterField(
            model_name='chatmessage',
            name='content',
            field=models.TextField(verbose_name='消息内容'),
        ),
        migrations.AlterField(
            model_name='chatmessage',
            name='sender_type',
            field=models.CharField(choices=[('user', '用户'), ('character', '角色')], max_length=10, verbose_name='发送者类型'),
        ),
        migrations.AlterField(
            model_name='chatmessage',
            name='sent_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='发送时间'),
        ),
        migrations.AlterField(
            model_name='chatmessage',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='prompttemplate',
            name='category',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='分类'),
        ),
        migrations.AlterField(
            model_name='prompttemplate',
            name='content',
            field=models.TextField(verbose_name='模板内容'),
        ),
        migrations.AlterField(
            model_name='prompttemplate',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='创建时间'),
        ),
        migrations.AlterField(
            model_name='prompttemplate',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_prompts', to=settings.AUTH_USER_MODEL, verbose_name='创建者'),
        ),
        migrations.AlterField(
            model_name='prompttemplate',
            name='description',
            field=models.TextField(blank=True, null=True, verbose_name='描述'),
        ),
        migrations.AlterField(
            model_name='prompttemplate',
            name='examples',
            field=models.JSONField(blank=True, default=dict, null=True, verbose_name='示例'),
        ),
        migrations.AlterField(
            model_name='prompttemplate',
            name='is_active',
            field=models.BooleanField(default=True, verbose_name='是否启用'),
        ),
        migrations.AlterField(
            model_name='prompttemplate',
            name='name',
            field=models.CharField(max_length=100, verbose_name='模板名称'),
        ),
        migrations.AlterField(
            model_name='prompttemplate',
            name='type',
            field=models.CharField(choices=[('image', '图片生成'), ('chat', '对话'), ('system', '系统')], max_length=50, verbose_name='模板类型'),
        ),
        migrations.AlterField(
            model_name='prompttemplate',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='更新时间'),
        ),
        migrations.AlterField(
            model_name='prompttemplate',
            name='variables',
            field=models.JSONField(blank=True, default=dict, null=True, verbose_name='变量配置'),
        ),
        migrations.AlterField(
            model_name='prompttemplate',
            name='version',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='版本'),
        ),
        migrations.AlterField(
            model_name='systemconfig',
            name='description',
            field=models.TextField(blank=True, null=True, verbose_name='描述'),
        ),
        migrations.AlterField(
            model_name='systemconfig',
            name='key',
            field=models.CharField(max_length=100, primary_key=True, serialize=False, verbose_name='配置键'),
        ),
        migrations.AlterField(
            model_name='systemconfig',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='更新时间'),
        ),
        migrations.AlterField(
            model_name='systemconfig',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='更新者'),
        ),
        migrations.AlterField(
            model_name='systemconfig',
            name='value',
            field=models.TextField(verbose_name='配置值'),
        ),
        migrations.AlterField(
            model_name='useradminrole',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='创建时间'),
        ),
        migrations.AlterField(
            model_name='useradminrole',
            name='role',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='users', to='core.adminrole', verbose_name='管理员角色'),
        ),
        migrations.AlterField(
            model_name='useradminrole',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='admin_roles', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='userprofile',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='创建时间'),
        ),
        migrations.AlterField(
            model_name='userprofile',
            name='deleted_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AlterField(
            model_name='userprofile',
            name='is_deleted',
            field=models.BooleanField(default=False, verbose_name='是否删除'),
        ),
        migrations.AlterField(
            model_name='userprofile',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='更新时间'),
        ),
        migrations.AlterField(
            model_name='userprofile',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
    ]
