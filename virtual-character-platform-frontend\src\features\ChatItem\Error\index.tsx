import { type IPluginErrorType } from '@lobehub/chat-plugin-sdk';
import type { AlertProps } from '@lobehub/ui';
import { Skeleton } from 'antd';
import { lazy, Suspense, memo, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { useProviderName } from '@/hooks/useProviderName';
import { AgentRuntimeErrorType, type ILobeAgentRuntimeErrorType } from '@/libs/agent-runtime';
import type { ErrorType } from '@/types/fetch';
import type { ChatMessage, ChatMessageError } from '@/types/message';

import ErrorJsonViewer from './ErrorJsonViewer';
import InvalidAPIKey from './InvalidAPIKey';

const loading = () => <Skeleton active />;

const OllamaBizError = lazy(() => import('./OllamaBizError'));

// Config for the errorMessage display
const getErrorAlertConfig = (
  errorType?: IPluginErrorType | ILobeAgentRuntimeErrorType | ErrorType,
): AlertProps | undefined => {
  // OpenAIBizError / ZhipuBizError / GoogleBizError / ...
  if (typeof errorType === 'string' && (errorType.includes('Biz') || errorType.includes('Invalid')))
    return {
      extraDefaultExpand: true,
      extraIsolate: true,
      type: 'warning',
    };

  switch (errorType) {
    case AgentRuntimeErrorType.PermissionDenied:
    case AgentRuntimeErrorType.QuotaLimitReached:
    case AgentRuntimeErrorType.LocationNotSupportError: {
      return {
        type: 'warning',
      };
    }

    default: {
      return undefined;
    }
  }
};

export const useErrorContent = (error: any) => {
  const { t } = useTranslation('error');
  const providerName = useProviderName(error?.body?.provider || '');

  return useMemo<AlertProps | undefined>(() => {
    if (!error) return;
    const messageError = error;

    const alertConfig = getErrorAlertConfig(messageError.type);

    return {
      message: t(`response.${messageError.type}` as any, { provider: providerName }),
      ...alertConfig,
    };
  }, [error]);
};

const ErrorMessageExtra = memo<{ data: ChatMessage }>(({ data }) => {
  const error = data.error as ChatMessageError;
  if (!error?.type) return;

  switch (error.type) {
    // case PluginErrorType.PluginSettingsInvalid: {
    //   return <PluginSettings id={data.id} plugin={data.plugin} />;
    // }

    case AgentRuntimeErrorType.OllamaBizError: {
      return (
        <Suspense fallback={loading()}>
          <OllamaBizError {...data} />
        </Suspense>
      );
    }
  }

  if (error.type.toString().includes('Invalid')) {
    return <InvalidAPIKey id={data.id} provider={data.error?.body?.provider} />;
  }

  return <ErrorJsonViewer error={data.error} id={data.id} />;
});

export default memo<{ data: ChatMessage }>(({ data }) => (
  <Suspense fallback={<Skeleton active style={{ width: '100%' }} />}>
    <ErrorMessageExtra data={data} />
  </Suspense>
));
