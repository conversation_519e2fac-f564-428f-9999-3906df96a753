from django.core.management.base import BaseCommand, CommandError
from backend_services.services.file_cleanup_service import FileCleanupService

class Command(BaseCommand):
    help = '清理已软删除且超过保留期限的角色图片'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='清理多少天前的已删除角色图片，默认为30天'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='仅测试运行，不实际删除文件'
        )

    def handle(self, *args, **options):
        retention_days = options['days']
        dry_run = options['dry_run']
        
        self.stdout.write(f"开始{'测试' if dry_run else ''}清理 {retention_days} 天前的已删除角色图片...")
        
        cleanup_service = FileCleanupService()
        
        if dry_run:
            # 仅打印将要删除的文件，不实际删除
            from datetime import timedelta
            from django.utils import timezone
            from core.models import Character
            
            cutoff_date = timezone.now() - timedelta(days=retention_days)
            expired_characters = Character.objects.filter(
                is_deleted=True,
                deleted_at__lt=cutoff_date,
                image_url__isnull=False
            ).exclude(image_url='')
            
            count = expired_characters.count()
            self.stdout.write(f"找到 {count} 个符合清理条件的图片:")
            
            for character in expired_characters:
                # Django模型自动生成id字段作为主键
                self.stdout.write(f"  - 角色ID: {character.id}, 图片URL: {character.image_url}")  # type: ignore
                
            self.stdout.write(self.style.SUCCESS("测试清理完成"))
        else:
            # 实际执行清理
            success, failure = cleanup_service.cleanup_deleted_character_images(retention_days=retention_days)
            
            if success > 0:
                self.stdout.write(self.style.SUCCESS(f'成功清理 {success} 个文件'))
            if failure > 0:
                self.stdout.write(self.style.WARNING(f'有 {failure} 个文件清理失败'))
            
            if success == 0 and failure == 0:
                self.stdout.write(self.style.SUCCESS("没有符合条件的文件需要清理"))