"""
日志存储方案模块 - 为虚拟角色平台提供生产环境日志收集与管理功能
"""
import os
import logging
import logging.handlers
import json
from datetime import datetime, timedelta
import gzip
import shutil
from pathlib import Path
from django.conf import settings


class LogStorageConfig:
    """日志存储配置类"""
    
    # 日志基本配置
    BASE_LOG_DIR = os.path.join(settings.BASE_DIR, 'logs')
    LOG_FILE_MAX_BYTES = 10 * 1024 * 1024  # 10MB
    LOG_FILE_BACKUP_COUNT = 10
    
    # 日志归档配置
    ARCHIVE_DIR = os.path.join(BASE_LOG_DIR, 'archive')
    LOG_RETENTION_DAYS = 30  # 日志保留天数
    
    # 日志文件名
    APP_LOG_FILE = os.path.join(BASE_LOG_DIR, 'app.log')
    ERROR_LOG_FILE = os.path.join(BASE_LOG_DIR, 'error.log')
    ACCESS_LOG_FILE = os.path.join(BASE_LOG_DIR, 'access.log')
    
    @classmethod
    def ensure_log_dirs(cls):
        """确保日志目录存在"""
        os.makedirs(cls.BASE_LOG_DIR, exist_ok=True)
        os.makedirs(cls.ARCHIVE_DIR, exist_ok=True)


def configure_production_logging():
    """
    配置生产环境日志系统
    
    在生产环境中，日志将输出到标准输出和文件，以便容器化环境的日志收集代理可以捕获
    """
    # 确保日志目录存在
    LogStorageConfig.ensure_log_dirs()
    
    # 获取根logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    
    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建JSON格式化器
    class JsonFormatter(logging.Formatter):
        def format(self, record):
            log_data = {
                'timestamp': datetime.fromtimestamp(record.created).isoformat(),
                'level': record.levelname,
                'logger': record.name,
                'message': record.getMessage(),
                'module': record.module,
                'service': getattr(settings, 'SERVICE_NAME', 'virtual-character-platform'),
            }
            
            # 添加异常信息
            if record.exc_info:
                log_data['error_type'] = record.exc_info[0].__name__
                log_data['error_message'] = str(record.exc_info[1])
                log_data['stack_trace'] = self.formatException(record.exc_info)
            
            # 添加额外字段
            if hasattr(record, 'extra'):
                log_data.update(record.extra)
            
            return json.dumps(log_data, ensure_ascii=False)
    
    # 创建标准输出处理器（用于容器环境）
    stdout_handler = logging.StreamHandler()
    stdout_handler.setFormatter(JsonFormatter())
    root_logger.addHandler(stdout_handler)
    
    # 创建应用日志文件处理器
    app_handler = logging.handlers.RotatingFileHandler(
        LogStorageConfig.APP_LOG_FILE,
        maxBytes=LogStorageConfig.LOG_FILE_MAX_BYTES,
        backupCount=LogStorageConfig.LOG_FILE_BACKUP_COUNT
    )
    app_handler.setFormatter(JsonFormatter())
    root_logger.addHandler(app_handler)
    
    # 创建错误日志文件处理器
    error_handler = logging.handlers.RotatingFileHandler(
        LogStorageConfig.ERROR_LOG_FILE,
        maxBytes=LogStorageConfig.LOG_FILE_MAX_BYTES,
        backupCount=LogStorageConfig.LOG_FILE_BACKUP_COUNT
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(JsonFormatter())
    root_logger.addHandler(error_handler)
    
    # 创建访问日志文件处理器
    access_logger = logging.getLogger('django.request')
    access_handler = logging.handlers.RotatingFileHandler(
        LogStorageConfig.ACCESS_LOG_FILE,
        maxBytes=LogStorageConfig.LOG_FILE_MAX_BYTES,
        backupCount=LogStorageConfig.LOG_FILE_BACKUP_COUNT
    )
    access_handler.setFormatter(JsonFormatter())
    access_logger.addHandler(access_handler)
    
    # 记录日志系统初始化完成
    root_logger.info("生产环境日志系统初始化完成")
    
    return root_logger


def archive_old_logs():
    """
    归档旧日志文件
    
    将超过保留期限的日志文件压缩并移动到归档目录
    """
    # 确保日志目录存在
    LogStorageConfig.ensure_log_dirs()
    
    # 获取当前时间
    now = datetime.now()
    
    # 获取日志目录中的所有日志文件
    log_dir = Path(LogStorageConfig.BASE_LOG_DIR)
    log_files = [f for f in log_dir.glob('*.log.*') if f.is_file()]
    
    # 归档旧日志文件
    for log_file in log_files:
        # 获取文件修改时间
        mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
        
        # 如果文件超过保留期限
        if now - mtime > timedelta(days=LogStorageConfig.LOG_RETENTION_DAYS):
            # 创建归档文件名
            archive_name = f"{log_file.name}_{mtime.strftime('%Y%m%d')}.gz"
            archive_path = os.path.join(LogStorageConfig.ARCHIVE_DIR, archive_name)
            
            # 压缩并归档日志文件
            with open(log_file, 'rb') as f_in:
                with gzip.open(archive_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            # 删除原始日志文件
            log_file.unlink()
            
            logging.info(f"归档日志文件: {log_file} -> {archive_path}")


def cleanup_archive():
    """
    清理归档目录中的旧文件
    
    删除超过保留期限两倍的归档文件
    """
    # 确保归档目录存在
    os.makedirs(LogStorageConfig.ARCHIVE_DIR, exist_ok=True)
    
    # 获取当前时间
    now = datetime.now()
    
    # 获取归档目录中的所有文件
    archive_dir = Path(LogStorageConfig.ARCHIVE_DIR)
    archive_files = [f for f in archive_dir.glob('*.gz') if f.is_file()]
    
    # 清理旧归档文件
    for archive_file in archive_files:
        # 获取文件修改时间
        mtime = datetime.fromtimestamp(archive_file.stat().st_mtime)
        
        # 如果文件超过保留期限的两倍
        if now - mtime > timedelta(days=LogStorageConfig.LOG_RETENTION_DAYS * 2):
            # 删除归档文件
            archive_file.unlink()
            
            logging.info(f"删除旧归档文件: {archive_file}") 