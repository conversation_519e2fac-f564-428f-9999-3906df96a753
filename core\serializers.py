from rest_framework import serializers
from .models import Character, CharacterBackground # Import the Character model

class CharacterGenerateSerializer(serializers.Serializer):
    race = serializers.CharField(max_length=100, required=False, allow_blank=True)
    anime_name = serializers.CharField(max_length=255, required=False, allow_blank=True)
    age = serializers.IntegerField(required=False, min_value=0)
    gender = serializers.CharField(max_length=50, required=False, allow_blank=True)
    identity = serializers.CharField(max_length=255, required=False, allow_blank=True)
    personality = serializers.CharField(max_length=255, required=False, allow_blank=True)

    def validate(self, data):
        # At least one of the fields should be provided for initial generation
        if not any([data.get('race'), data.get('anime_name'), data.get('age'), data.get('gender'), data.get('identity'), data.get('personality')]):
            raise serializers.ValidationError("At least one parameter (race, anime_name, age, gender, identity, personality) is required for character generation.")
        return data

class CharacterSaveSerializer(serializers.ModelSerializer):
    # 使image_url可选，允许空值，并允许相对路径
    image_url = serializers.Char<PERSON>ield(max_length=500, required=False, allow_blank=True, allow_null=True)

    class Meta:
        model = Character
        fields = ('id', 'name', 'image_url', 'age', 'gender', 'identity', 'personality', 'appearance_params', 'settings', 'public')
        read_only_fields = ('id',)

    def validate_personality(self, value):
        """验证性格值"""
        from .models import VALID_PERSONALITIES
        if value not in VALID_PERSONALITIES:
            raise serializers.ValidationError(f"无效的性格类型: {value}。有效选项: {', '.join(VALID_PERSONALITIES)}")
        return value

    def validate_identity(self, value):
        """验证身份值"""
        from .models import VALID_IDENTITIES
        if value not in VALID_IDENTITIES:
            raise serializers.ValidationError(f"无效的身份类型: {value}。有效选项: {', '.join(VALID_IDENTITIES)}")
        return value

    def validate_image_url(self, value):
        """验证图像URL，允许相对路径和绝对URL"""
        if not value:
            return value

        # 去除前后空格
        value = value.strip()

        # 允许相对路径（以/开头）或完整的HTTP/HTTPS URL
        import re
        if value.startswith('/') or re.match(r'^https?://\S+$', value):
            return value
        else:
            raise serializers.ValidationError("图像URL格式不正确，请提供有效的URL或相对路径")

    def create(self, validated_data):
        # The 'user' field will be added in the view from the request.user
        return Character.objects.create(**validated_data)

class CharacterListSerializer(serializers.ModelSerializer):
    class Meta:
        model = Character
        fields = ('id', 'name', 'image_url', 'age', 'gender', 'identity', 'personality', 'public', 'created_at')

class CharacterDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = Character
        fields = ('id', 'name', 'image_url', 'age', 'gender', 'identity', 'personality', 'appearance_params', 'settings', 'public', 'user', 'created_at', 'updated_at')
        read_only_fields = ('id', 'user', 'created_at', 'updated_at')

class ChatMessageSerializer(serializers.Serializer):
    user_message = serializers.CharField(max_length=1000, required=True)

class CommunityCharacterListSerializer(serializers.ModelSerializer):
    creator_name = serializers.CharField(source='user.username', read_only=True)
    creator_id = serializers.IntegerField(source='user.id', read_only=True)
    is_liked = serializers.SerializerMethodField()

    class Meta:
        model = Character
        fields = (
            'id', 'name', 'image_url', 'age', 'gender', 'identity', 'personality',
            'public', 'created_at', 'creator_name', 'creator_id', 'is_liked',
            'category', 'tags', 'rating', 'downloads', 'is_official',
            'likes_count', 'chat_count', 'view_count', 'greeting', 'system_role',
            'readme', 'homepage', 'llm_provider', 'llm_model', 'llm_temperature', 'llm_max_tokens'
        )

    def get_is_liked(self, obj):
        """获取当前用户是否点赞了该角色"""
        # 这里可以根据实际需求实现点赞逻辑
        # 暂时返回False，后续可以扩展用户点赞功能
        return False


class CharacterBackgroundSerializer(serializers.ModelSerializer):
    """角色背景图片序列化器"""
    scene_name = serializers.CharField(source='get_scene_display_name', read_only=True)

    class Meta:
        model = CharacterBackground
        fields = (
            'id',
            'scene_type',
            'scene_name',
            'image_url',
            'generation_status',
            'created_at',
            'updated_at',
            'error_message'
        )
        read_only_fields = (
            'id',
            'scene_name',
            'generation_status',
            'created_at',
            'updated_at',
            'error_message'
        )