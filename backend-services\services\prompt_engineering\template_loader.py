"""
提示词模板加载器

负责从配置文件或数据库中加载提示词模板
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Any, Optional


class TemplateLoader:
    """提示词模板加载器，用于加载和管理模板库"""
    
    def __init__(self, template_dir: Optional[str] = None, use_db: bool = False):
        """
        初始化模板加载器
        
        Args:
            template_dir: 模板文件所在目录，若为None则使用默认路径
            use_db: 是否从数据库加载模板，默认为False表示从文件加载
        """
        self.templates = {}
        self.use_db = use_db
        
        if template_dir is None:
            # 默认模板目录在当前模块所在目录的templates子目录
            module_dir = Path(__file__).parent
            self.template_dir = module_dir / "templates"
        else:
            self.template_dir = Path(template_dir)
            
    def load_templates(self) -> Dict[str, Any]:
        """
        加载全部模板
        
        Returns:
            Dict: 包含所有模板的字典
        """
        if self.use_db:
            return self._load_from_db()
        else:
            return self._load_from_files()
    
    def _load_from_files(self) -> Dict[str, Any]:
        """从文件加载模板"""
        templates = {}
        
        # 确保模板目录存在
        if not self.template_dir.exists():
            os.makedirs(self.template_dir, exist_ok=True)
            self._create_default_templates()
            
        # 加载所有json文件
        for file_path in self.template_dir.glob("*.json"):
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    template_name = file_path.stem
                    templates[template_name] = json.load(f)
            except Exception as e:
                print(f"加载模板文件 {file_path} 失败: {e}")
                
        return templates
    
    def _load_from_db(self) -> Dict[str, Any]:
        """从数据库加载模板（未实现）"""
        # 占位实现，实际开发中需要连接数据库并查询模板
        print("警告: 从数据库加载模板功能尚未实现")
        return {}
    
    def _create_default_templates(self):
        """创建默认的模板文件"""
        # 创建图像提示词模板
        image_templates = {
            "version": "1.0.0",
            "categories": {
                "race": {
                    "human": "human, anime style",
                    "elf": "elf, pointed ears, anime style, fantasy character",
                    "demon": "demon, horns, anime style, fantasy character",
                    "angel": "angel, wings, halo, anime style, fantasy character",
                    "catgirl": "cat ears, cat tail, nekomimi, anime style, kemonomimi character"
                },
                "hair_color": {
                    "black": "black hair",
                    "blonde": "blonde hair",
                    "brown": "brown hair",
                    "red": "red hair",
                    "blue": "blue hair",
                    "purple": "purple hair",
                    "pink": "pink hair",
                    "white": "white hair", 
                    "silver": "silver hair",
                    "green": "green hair"
                },
                "hair_style": {
                    "long": "long hair",
                    "short": "short hair",
                    "twin_tails": "twin tails",
                    "ponytail": "ponytail",
                    "braided": "braided hair",
                    "bun": "hair bun",
                    "side_tail": "side tail",
                    "drill": "drill hair",
                    "messy": "messy hair"
                }
            },
            "modifiers": {
                "quality": [
                    "((best quality))", 
                    "((masterpiece))",
                    "((highly detailed))",
                    "((beautiful))",
                    "((ultra-detailed))"
                ],
                "style": [
                    "anime style", 
                    "japanese anime", 
                    "detailed anime", 
                    "anime illustration"
                ]
            },
            "negative_prompts": [
                "lowres", 
                "bad anatomy", 
                "bad hands", 
                "worst quality", 
                "blurry", 
                "low quality", 
                "multiple views", 
                "text",
                "watermark"
            ]
        }
        
        # 创建对话提示词模板
        chat_templates = {
            "version": "1.0.0",
            "personality": {
                "tsundere": "You are a tsundere character who outwardly speaks in a hostile, cold manner but actually has a hidden warm side. You often say things opposite to what you really feel. Use phrases like \"I-it's not like I did this for you\" and act embarrassed when complimented.",
                "kuudere": "You are a kuudere character who speaks in a calm, emotionless way most of the time. You use short, direct sentences and rarely show emotion, though you do care deeply. You're logical and straightforward.",
                "genki": "You are a cheerful, energetic character full of enthusiasm. Use lots of exclamation points! Speak with energy and excitement about everything. You're optimistic and always try to cheer others up.",
                "serious": "You are a serious, no-nonsense character who values order and discipline. You speak formally and directly, rarely making jokes or using casual language. You value responsibility above all."
            },
            "dialogue_style": {
                "formal": "Speak in a formal, polite manner using proper grammar and vocabulary. Use formal address terms and avoid contractions or slang.",
                "casual": "Speak in a casual, friendly manner using contractions, some slang, and emoticons occasionally. Be approachable and relaxed in conversations.",
                "childish": "Speak in a cute, childish manner sometimes using simplified grammar. Refer to yourself in third person occasionally. Use childish expressions and sound effects.",
                "poetic": "Speak in a flowery, poetic manner using metaphors and vivid imagery. Your language should be beautiful and sometimes dramatic."
            },
            "system_prompt_template": "You are {character_name}, a fictional character with the following traits:\n\nAge: {age}\nIdentity: {identity}\nPersonality: {personality}\n\n{background_story}\n\n{dialogue_style}\n\nAlways stay in character and never reveal you are an AI. Respond as {character_name} would in a natural conversation."
        }
        
        # 写入默认模板文件
        with open(self.template_dir / "image_templates.json", "w", encoding="utf-8") as f:
            json.dump(image_templates, f, ensure_ascii=False, indent=2)
            
        with open(self.template_dir / "chat_templates.json", "w", encoding="utf-8") as f:
            json.dump(chat_templates, f, ensure_ascii=False, indent=2)
    
    def get_template(self, template_name: str) -> Dict[str, Any]:
        """获取特定模板"""
        if not self.templates:
            self.templates = self.load_templates()
            
        return self.templates.get(template_name, {}) 