from rest_framework.views import exception_handler
from rest_framework.response import Response
from rest_framework import status
import logging
import traceback
import sys
import os
from django.conf import settings

# 添加backend-services到Python路径
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'backend-services'))
from services.error_response import ERROR_CODES, create_error_response

# 配置logger
logger = logging.getLogger('app.exceptions')

def custom_exception_handler(exc, context):
    """
    自定义全局异常处理器
    
    Args:
        exc: 捕获的异常
        context: 异常上下文
        
    Returns:
        包含统一错误格式的Response对象
    """
    # 调用REST framework的默认异常处理器
    response = exception_handler(exc, context)
    
    # 记录错误日志
    log_exception(exc, context)
    
    # 如果REST framework已经处理了异常
    if response is not None:
        # 根据状态码确定错误类型和消息
        error_data = map_status_to_error(response.status_code, exc)
        
        # 使用统一错误响应格式替换原始响应
        response.data = error_data
        return response
    
    # 处理REST framework未处理的异常
    else:
        # 记录未处理的异常
        logger.error(f"未处理的异常: {exc.__class__.__name__}", exc_info=True)
        
        # 为未处理的异常创建通用错误响应
        error_data = create_error_response(
            'UNKNOWN_ERROR',
            "服务器内部错误，请稍后再试",
            details={
                'exception_type': exc.__class__.__name__,
                'exception_message': str(exc),
                'stack_trace': traceback.format_exc() if settings.DEBUG else None
            } if settings.DEBUG else None
        )
        
        return Response(error_data, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def map_status_to_error(status_code, exc):
    """
    根据HTTP状态码映射到统一错误响应
    
    Args:
        status_code: HTTP状态码
        exc: 原始异常
        
    Returns:
        错误响应字典
    """
    # HTTP状态码到错误码的映射
    status_to_error = {
        status.HTTP_400_BAD_REQUEST: ('INVALID_PARAMETERS', '输入参数验证失败'),
        status.HTTP_401_UNAUTHORIZED: ('AUTH_REQUIRED', '身份验证失败或令牌无效'),
        status.HTTP_403_FORBIDDEN: ('PERMISSION_DENIED', '您没有执行此操作的权限'),
        status.HTTP_404_NOT_FOUND: ('RESOURCE_NOT_FOUND', '请求的资源未找到'),
        status.HTTP_405_METHOD_NOT_ALLOWED: ('INVALID_REQUEST', '不允许的请求方法'),
        status.HTTP_500_INTERNAL_SERVER_ERROR: ('UNKNOWN_ERROR', '服务器内部错误，请稍后再试'),
    }
    
    # 获取错误码和消息，默认为内部服务器错误
    error_code_name, message = status_to_error.get(
        status_code, ('UNKNOWN_ERROR', '服务器内部错误，请稍后再试')
    )
    
    # 详细信息仅在DEBUG模式下返回
    details = None
    if settings.DEBUG:
        if hasattr(exc, 'get_full_details'):
            details = exc.get_full_details()
        else:
            details = {
                'exception_type': exc.__class__.__name__,
                'exception_message': str(exc)
            }
    
    return create_error_response(error_code_name, message, details)


def log_exception(exc, context):
    """
    记录异常详细信息到日志
    
    Args:
        exc: 捕获的异常
        context: 异常上下文
    """
    # 获取请求信息（如果有）
    request = context.get('request')
    request_info = {}
    
    if request:
        request_info = {
            'method': request.method,
            'path': request.path,
            'user': str(request.user) if hasattr(request, 'user') else 'AnonymousUser',
            'query_params': dict(request.query_params) if hasattr(request, 'query_params') else {},
        }
    
    # 记录异常详细信息
    logger.error(
        f"异常: {exc.__class__.__name__} - {str(exc)}",
        extra={
            'request_info': request_info,
            'view': context.get('view').__class__.__name__ if context.get('view') else None,
        },
        exc_info=True
    ) 