"""
提供用户认证和授权相关的API视图：
- 用户注册
- 用户登录
- 用户注销
- 密码修改
"""
from rest_framework import status, views
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth.models import User
from django.contrib.auth import authenticate
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from typing import Optional, Dict, Any

from core.auth.serializers import (
    UserRegistrationSerializer,
    UserLoginSerializer,
    ChangePasswordSerializer,
    AdminLoginSerializer,
)
from core.auth.services.token_service import TokenService
from core.auth.services.token_blacklist_service import TokenBlacklistService
from core.auth.services.admin_service import AdminService

@method_decorator(csrf_exempt, name='dispatch')
class RegisterView(views.APIView):
    """用户注册API视图"""

    # 允许未认证的用户访问
    permission_classes = [AllowAny]
    
    def post(self, request):
        """处理POST请求，创建新用户"""
        serializer = UserRegistrationSerializer(data=request.data)

        if serializer.is_valid():
            try:
                # 创建用户
                user = serializer.save()

                # 生成登录Token，使用Simple JWT保持一致性
                refresh = RefreshToken.for_user(user)
                access_token = refresh.access_token  # type: ignore

                # 返回成功响应
                return Response({
                    'message': '注册成功',
                    'user': {
                        'id': user.id,  # type: ignore
                        'username': user.username,  # type: ignore
                        'email': getattr(user, 'email', '')
                    },
                    'token': str(access_token),
                    'refresh': str(refresh)
                }, status=status.HTTP_201_CREATED)
                
            except ValueError as e:
                # 处理创建用户过程中的错误
                return Response({
                    'error': str(e)
                }, status=status.HTTP_400_BAD_REQUEST)
        
        # 返回验证错误
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@method_decorator(csrf_exempt, name='dispatch')
class LoginView(views.APIView):
    """用户登录API视图"""

    # 允许未认证的用户访问
    permission_classes = [AllowAny]
    
    def post(self, request):
        """处理POST请求，验证用户登录"""
        serializer = UserLoginSerializer(data=request.data)

        if serializer.is_valid():
            try:
                # 获取验证数据（序列化器验证通过后，validated_data不会为空）
                username = serializer.validated_data['username']  # type: ignore
                password = serializer.validated_data['password']  # type: ignore

                # 使用Django的认证系统验证用户
                user = authenticate(username=username, password=password)

                if user is not None:
                    # 验证成功，使用Simple JWT生成Token
                    refresh = RefreshToken.for_user(user)
                    access_token = refresh.access_token  # type: ignore

                    # 返回成功响应
                    return Response({
                        'message': '登录成功',
                        'user': {
                            'id': user.id,  # type: ignore
                            'username': user.username,  # type: ignore
                            'email': getattr(user, 'email', '')
                        },
                        'token': str(access_token),
                        'refresh': str(refresh)
                    })
                else:
                    # 验证失败，返回通用错误信息
                    # 出于安全考虑，不明确提示是用户名不存在还是密码错误
                    return Response({
                        'error': '用户名或密码不正确'
                    }, status=status.HTTP_401_UNAUTHORIZED)
            except Exception as e:
                return Response({
                    'error': '登录过程中发生错误'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # 返回验证错误
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class LogoutView(views.APIView):
    """用户注销API视图"""
    
    # 只允许已认证的用户访问
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """处理POST请求，注销用户"""
        # 从请求头获取Token
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
        if auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]

            # 验证Token并获取payload
            payload = TokenService.validate_token(token)

            if payload is not None:
                # 将Token添加到黑名单
                if TokenBlacklistService.add_to_blacklist(payload):
                    # 注销成功
                    return Response({
                        'message': '注销成功'
                    })
                else:
                    # 添加到黑名单失败
                    return Response({
                        'error': '注销失败，请稍后重试'
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            else:
                # Token无效
                return Response({
                    'error': '无效的认证信息'
                }, status=status.HTTP_401_UNAUTHORIZED)
        else:
            # 没有提供Token
            return Response({
                'error': '未提供认证信息'
            }, status=status.HTTP_401_UNAUTHORIZED)

class ChangePasswordView(views.APIView):
    """用户密码修改API视图"""
    
    # 只允许已认证的用户访问
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """处理POST请求，修改用户密码"""
        serializer = ChangePasswordSerializer(data=request.data)

        if serializer.is_valid():
            # 获取验证后的数据（序列化器验证通过后，validated_data不会为空）
            old_password = serializer.validated_data['old_password']  # type: ignore
            new_password = serializer.validated_data['new_password']  # type: ignore

            # 获取当前用户
            user = request.user
            
            # 导入用户服务
            from core.auth.services.user_service import UserService
            
            try:
                # 更新密码
                UserService.update_password(user, old_password, new_password)
                
                # 从请求头获取Token
                auth_header = request.META.get('HTTP_AUTHORIZATION', '')
                if auth_header.startswith('Bearer '):
                    token = auth_header.split(' ')[1]

                    # 验证Token并获取payload
                    payload = TokenService.validate_token(token)

                    if payload is not None:
                        # 将当前Token添加到黑名单，强制用户重新登录
                        TokenBlacklistService.add_to_blacklist(payload)
                
                # 返回成功响应
                return Response({
                    'message': '密码修改成功，请重新登录'
                })
                
            except ValueError as e:
                # 处理密码更新过程中的错误
                return Response({
                    'error': str(e)
                }, status=status.HTTP_400_BAD_REQUEST)
        
        # 返回验证错误
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class AdminLoginView(views.APIView):
    """管理员登录API视图"""
    
    # 允许未认证的用户访问
    permission_classes = [AllowAny]
    
    def post(self, request):
        """处理POST请求，验证管理员登录"""
        serializer = AdminLoginSerializer(data=request.data)

        if serializer.is_valid():
            # 获取验证数据（序列化器验证通过后，validated_data不会为空）
            username = serializer.validated_data['username']  # type: ignore
            password = serializer.validated_data['password']  # type: ignore
            
            # 使用管理员服务验证管理员身份
            login_result = AdminService.login_admin(username, password)

            if login_result is not None:
                # 验证成功，返回管理员信息和Token
                return Response({
                    'message': '管理员登录成功',
                    'user': login_result['user'],
                    'token': login_result['token']
                })
            else:
                # 验证失败，返回通用错误信息
                return Response({
                    'error': '用户名或密码不正确，或者您没有管理员权限'
                }, status=status.HTTP_401_UNAUTHORIZED)
        
        # 返回验证错误
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)