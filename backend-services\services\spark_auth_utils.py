import hashlib
import hmac
import base64
from datetime import datetime, timezone
import json
from typing import Dict, Any

class SparkAuthUtils:
    """
    星火 API 鉴权工具类，用于生成请求签名。
    """

    @staticmethod
    def generate_signature(
        api_key: str,
        api_secret: str,
        host: str,
        request_uri: str,
        http_method: str,
        digest: str = ""
    ) -> str:
        """
        生成星火 API 的签名。

        Args:
            api_key (str): 应用的 API Key。
            api_secret (str): 应用的 API Secret。
            host (str): 请求的主机名，例如 "image.xf-yun.com"。
            request_uri (str): 请求的 URI 路径，例如 "/v2/iat"。
            http_method (str): HTTP 请求方法，例如 "POST"。
            digest (str): 请求体的 SHA256 摘要，如果请求体为空则为 ""。

        Returns:
            str: 生成的鉴权签名。
        """
        # 1. 获取当前时间 (UTC)，并格式化
        current_time_str = datetime.now(timezone.utc).strftime("%a, %d %b %Y %H:%M:%S GMT")

        # 2. 构造待签名字符串
        # 由于星火 API 的鉴权签名机制可能与标准有所差异，这里按照常见模式构建
        # 实际应根据星火 API 文档进行精确调整
        # 通常包含 host, date, request-line, digest 等
        # 这里简化为 Content-MD5 (如果有), Request-Line (Method + URI), Date, Host

        # For image generation, usually it's application/json content type
        # and the digest is based on the JSON payload.
        # However, Spark API documentation might specify a different digest calculation
        # or even skip it for certain endpoints.

        # Placeholder for building the string to sign. This needs to be precise based on Spark API docs.
        # Assuming a general signature format for demonstration.
        signature_origin = (
            f"host: {host}"
            f"\ndate: {current_time_str}"
            f"\n{http_method} {request_uri} HTTP/1.1"
        )
        if digest:
            signature_origin += f"\ndigest: {digest}"

        # 3. HMAC-SHA256 签名
        hmac_hash = hmac.new(api_secret.encode('utf-8'), signature_origin.encode('utf-8'), hashlib.sha256)
        signature = base64.b64encode(hmac_hash.digest()).decode('utf-8')

        # 返回Authorization header部分的内容，实际使用中可能还需要拼接其他参数
        # 例如 algorithm="hmac-sha256", headers="host date request-line"
        # 鉴于TASK003的描述是"返回有效的鉴权头或参数"，这里返回核心签名部分
        return signature

    @staticmethod
    def generate_auth_header(
        api_key: str,
        api_secret: str,
        host: str,
        request_uri: str,
        http_method: str,
        digest: str = ""
    ) -> Dict[str, str]:
        """
        生成完整的鉴权 Header。

        Args:
            api_key (str): 应用的 API Key。
            api_secret (str): 应用的 API Secret。
            host (str): 请求的主机名，例如 "image.xf-yun.com"。
            request_uri (str): 请求的 URI 路径，例如 "/v2/iat"。
            http_method (str): HTTP 请求方法，例如 "POST"。
            digest (str): 请求体的 SHA256 摘要，如果请求体为空则为 ""。

        Returns:
            dict: 包含 Authorization 和 Date 的请求头。
        """
        current_time_str = datetime.now(timezone.utc).strftime("%a, %d %b %Y %H:%M:%S GMT")
        signature = SparkAuthUtils.generate_signature(api_key, api_secret, host, request_uri, http_method, digest)

        auth_header = f'api_key="{api_key}", algorithm="hmac-sha256", headers="host date request-line", signature="{signature}"'
        if digest:
            auth_header = f'api_key="{api_key}", algorithm="hmac-sha256", headers="host date request-line digest", signature="{signature}"'

        return {
            "Authorization": auth_header,
            "Date": current_time_str,
            "Host": host
        }

    @staticmethod
    def generate_digest(payload: Dict[str, Any]) -> str:
        """
        生成请求体的 SHA256 摘要。

        Args:
            payload (dict): 请求的 JSON payload。

        Returns:
            str: base64 编码的 SHA256 摘要。
        """
        # 将 payload 转换为 JSON 字符串，并进行字典键排序以确保一致性
        payload_str = json.dumps(payload, ensure_ascii=False, separators=(',', ':'))
        
        # SHA256 哈希
        sha256_hash = hashlib.sha256(payload_str.encode('utf-8')).digest()
        
        # Base64 编码
        digest_base64 = base64.b64encode(sha256_hash).decode('utf-8')
        return digest_base64 