"""
Token黑名单服务，用于管理已注销但尚未过期的Token。

在生产环境中，这个服务应该使用Redis等缓存系统实现，
但为了简化开发，这里使用内存存储实现一个基本版本。
"""
import logging
import threading
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

class TokenBlacklistService:
    """Token黑名单服务"""
    
    # 使用类变量存储黑名单Token
    # 格式: {token_jti: expiration_timestamp}
    _blacklist = {}
    _lock = threading.RLock()  # 使用可重入锁保护黑名单访问
    
    @classmethod
    def add_to_blacklist(cls, token_payload):
        """
        将Token添加到黑名单。

        Args:
            token_payload: Token的payload部分，包含jti和exp字段

        Returns:
            bool: 是否成功添加到黑名单
        """
        try:
            if token_payload is None:
                logger.error("Token payload为空")
                return False

            jti = token_payload.get('jti')
            exp = token_payload.get('exp')

            if not jti or not exp:
                logger.error("Token缺少必要字段")
                return False

            with cls._lock:
                cls._blacklist[jti] = exp
                logger.info(f"Token {jti[:8]}... 已加入黑名单")

            # 启动清理过期Token的任务
            cls._cleanup_expired_tokens()
            return True

        except Exception as e:
            logger.error(f"将Token添加到黑名单时出错: {str(e)}")
            return False
    
    @classmethod
    def is_blacklisted(cls, token_payload):
        """
        检查Token是否在黑名单中。

        Args:
            token_payload: Token的payload部分，包含jti字段

        Returns:
            bool: 如果Token在黑名单中返回True，否则返回False
        """
        try:
            if token_payload is None:
                logger.error("Token payload为空")
                return False

            jti = token_payload.get('jti')

            if not jti:
                logger.error("Token缺少jti字段")
                return False

            with cls._lock:
                return jti in cls._blacklist

        except Exception as e:
            logger.error(f"检查Token黑名单状态时出错: {str(e)}")
            return False
    
    @classmethod
    def _cleanup_expired_tokens(cls):
        """
        清理黑名单中已过期的Token。
        在生产环境中，这应该由定时任务处理。
        """
        try:
            current_time = datetime.now(timezone.utc).timestamp()
            expired_jtis = []
            
            with cls._lock:
                # 找出所有已过期的Token
                for jti, exp_time in cls._blacklist.items():
                    if exp_time < current_time:
                        expired_jtis.append(jti)
                
                # 从黑名单中移除已过期的Token
                for jti in expired_jtis:
                    del cls._blacklist[jti]
                    
            if expired_jtis:
                logger.info(f"已从黑名单中清理 {len(expired_jtis)} 个过期Token")
                
        except Exception as e:
            logger.error(f"清理过期Token时出错: {str(e)}")