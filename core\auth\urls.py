"""
认证模块的URL路由配置。
"""
from django.urls import path
from core.auth.views import (
    RegisterView,
    LoginView,
    LogoutView,
    ChangePasswordView,
    AdminLoginView,
)

urlpatterns = [
    # 注册接口
    path('register/', RegisterView.as_view(), name='register'),
    # 登录接口
    path('login/', LoginView.as_view(), name='login'),
    # 注销接口
    path('logout/', LogoutView.as_view(), name='logout'),
    # 修改密码接口
    path('change_password/', ChangePasswordView.as_view(), name='change_password'),
]

# 管理员相关路由
admin_urlpatterns = [
    path('admin/login', AdminLoginView.as_view(), name='admin-login'),
]

# 合并所有路由
urlpatterns.extend(admin_urlpatterns) 