"""
自定义业务异常类模块 - 为虚拟角色平台提供业务相关的异常类
"""
from .error_response import ERROR_CODES


class BaseAppException(Exception):
    """
    应用基础异常类，所有自定义异常都应继承此类
    """
    def __init__(self, message=None, code_name=None, details=None):
        """
        初始化异常
        
        Args:
            message: 错误消息
            code_name: 错误代码名称，必须在ERROR_CODES中定义
            details: 可选的错误详情
        """
        self.code_name = code_name or 'UNKNOWN_ERROR'
        self.message = message or "发生未知错误"
        self.details = details
        super().__init__(self.message)
    
    @property
    def error_code(self):
        """获取数字错误代码"""
        return ERROR_CODES.get(self.code_name, ERROR_CODES['UNKNOWN_ERROR'])


# 通用错误
class InvalidParametersError(BaseAppException):
    """无效参数错误"""
    def __init__(self, message="提供的参数无效", details=None):
        super().__init__(message, 'INVALID_PARAMETERS', details)


class ResourceNotFoundError(BaseAppException):
    """资源未找到错误"""
    def __init__(self, message="请求的资源未找到", resource_type=None, resource_id=None):
        details = None
        if resource_type and resource_id:
            details = {'resource_type': resource_type, 'resource_id': resource_id}
            message = f"未找到{resource_type}: {resource_id}"
        super().__init__(message, 'RESOURCE_NOT_FOUND', details)


class PermissionDeniedError(BaseAppException):
    """权限拒绝错误"""
    def __init__(self, message="您没有执行此操作的权限", details=None):
        super().__init__(message, 'PERMISSION_DENIED', details)


# 用户与认证相关错误
class AuthenticationError(BaseAppException):
    """认证错误"""
    def __init__(self, message="身份验证失败", details=None):
        super().__init__(message, 'AUTH_FAILED', details)


class UserNotFoundError(BaseAppException):
    """用户未找到错误"""
    def __init__(self, user_id=None):
        message = "未找到用户"
        details = None
        if user_id:
            message = f"未找到用户: {user_id}"
            details = {'user_id': user_id}
        super().__init__(message, 'USER_NOT_FOUND', details)


# 角色相关错误
class CharacterNotFoundError(BaseAppException):
    """角色未找到错误"""
    def __init__(self, character_id=None):
        message = "未找到角色"
        details = None
        if character_id:
            message = f"未找到角色: {character_id}"
            details = {'character_id': character_id}
        super().__init__(message, 'CHARACTER_NOT_FOUND', details)


class CharacterCreateError(BaseAppException):
    """角色创建错误"""
    def __init__(self, message="角色创建失败", details=None):
        super().__init__(message, 'CHARACTER_CREATE_FAILED', details)


class CharacterUpdateError(BaseAppException):
    """角色更新错误"""
    def __init__(self, message="角色更新失败", details=None):
        super().__init__(message, 'CHARACTER_UPDATE_FAILED', details)


# 交互相关错误
class InteractionError(BaseAppException):
    """交互错误"""
    def __init__(self, message="与角色交互失败", details=None):
        super().__init__(message, 'INTERACTION_FAILED', details)


class ResponseGenerationError(BaseAppException):
    """响应生成错误"""
    def __init__(self, message="生成响应失败", details=None):
        super().__init__(message, 'RESPONSE_GENERATION_FAILED', details)


# 外部服务相关错误
class ExternalApiError(BaseAppException):
    """外部API错误"""
    def __init__(self, service_name=None, message=None, details=None):
        if service_name and not message:
            message = f"调用外部服务 {service_name} 失败"
        elif not message:
            message = "调用外部服务失败"
        
        if service_name and not details:
            details = {'service_name': service_name}
            
        super().__init__(message, 'EXTERNAL_API_ERROR', details)


class ImageGenerationError(BaseAppException):
    """图像生成错误"""
    def __init__(self, message="图像生成失败", details=None):
        super().__init__(message, 'IMAGE_GENERATION_FAILED', details)


class VoiceGenerationError(BaseAppException):
    """语音生成错误"""
    def __init__(self, message="语音生成失败", details=None):
        super().__init__(message, 'VOICE_GENERATION_FAILED', details) 