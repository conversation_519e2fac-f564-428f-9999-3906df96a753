from django.shortcuts import render, get_object_or_404
from django.http import HttpResponse
import os
import base64
import uuid
from django.conf import settings
from .spark_api_auth import generate_spark_signature # Import the new function
import json # Import json for json.dumps in test_spark_signature_view
from typing import Any, Dict, Optional, TYPE_CHECKING

if TYPE_CHECKING:
    from django.db.models.manager import Manager

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, generics
from django.db import models
from .serializers import CharacterGenerateSerializer, CharacterSaveSerializer, CharacterListSerializer, CharacterDetailSerializer, ChatMessageSerializer, CommunityCharacterListSerializer # Import the new serializer
from .models import Character, ChatMessage, CharacterBackground, VALID_PERSONALITIES, VALID_IDENTITIES # Import the Character model, ChatMessage and valid values lists
from .prompt_engineering.personality_prompts import apply_personality_to_image_prompt, apply_personality_to_dialogue_prompt # 导入性格提示词处理函数
from .prompt_engineering.identity_prompts import apply_identity_to_image_prompt, combine_personality_identity_image_prompts, apply_identity_to_dialogue_prompt, combine_personality_identity_dialogue_prompts # 导入身份提示词处理函数
import requests # Import requests for making HTTP calls
from rest_framework.permissions import IsAuthenticated # Import IsAuthenticated
import logging

# Create your views here.
logger = logging.getLogger(__name__)

def test_env_view(request):
    test_var = os.environ.get('TEST_VAR', 'TEST_VAR not found')

    # Get Spark API credentials from Django settings using correct attribute names
    spark_app_id = getattr(settings, 'SPARK_APP_ID', None)
    spark_api_key = getattr(settings, 'SPARK_API_KEY', None)
    spark_api_secret = getattr(settings, 'SPARK_API_SECRET', None)

    # For security, only display a partial string of the keys
    # 确保在对字符串进行操作前，变量不是 None，并提供一个友好的默认显示值
    display_app_id = f"{spark_app_id[:4]}...{spark_app_id[-4:]}" if spark_app_id and len(spark_app_id) > 8 else (spark_app_id or 'Not Set')
    display_api_key = f"{spark_api_key[:4]}...{spark_api_key[-4:]}" if spark_api_key and len(spark_api_key) > 8 else (spark_api_key or 'Not Set')
    display_api_secret = f"{spark_api_secret[:4]}...{spark_api_secret[-4:]}" if spark_api_secret and len(spark_api_secret) > 8 else (spark_api_secret or 'Not Set')


    response_content = f"""
    The value of TEST_VAR is: {test_var}<br>
    Spark APP ID: {display_app_id}<br>
    Spark API Key: {display_api_key}<br>
    Spark API Secret: {display_api_secret}
    """
    return HttpResponse(response_content)


def test_spark_signature_view(request):
    # Get Spark API credentials from Django settings using correct attribute names
    app_id = getattr(settings, 'SPARK_APP_ID', '')
    api_key = getattr(settings, 'SPARK_API_KEY', '')
    api_secret = getattr(settings, 'SPARK_API_SECRET', '')

    # Dummy values for testing signature generation
    test_url = "https://spark-api.xf-yun.com/v2.1/tts" # Example URL for a Spark API
    test_body = {"header": {"app_id": app_id}, "parameter": {}, "payload": {}} # Example request body

    try:
        auth_headers = generate_spark_signature(app_id, api_key, api_secret, test_url, test_body)
        response_content = f"Spark API Signature Headers:<br><pre>{json.dumps(auth_headers, indent=2)}</pre>"
    except Exception as e:
        response_content = f"Error generating signature: {e}"

    return HttpResponse(response_content)


class CharacterGenerateView(APIView):
    serializer_class = CharacterGenerateSerializer
    # 在测试环境中不需要认证
    permission_classes = []  # 明确指定不需要认证
    authentication_classes = []  # 覆盖默认的认证类

    def post(self, request, *args, **kwargs):  # type: ignore
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Extract validated data (序列化器验证通过后，validated_data不会为空)
        validated_data = serializer.validated_data

        race = validated_data.get('race')  # type: ignore
        anime_name = validated_data.get('anime_name')  # type: ignore
        age = validated_data.get('age')  # type: ignore
        gender = validated_data.get('gender')  # type: ignore
        identity = validated_data.get('identity')  # type: ignore
        personality = validated_data.get('personality')  # type: ignore

        # --- Preliminary Prompt Engineering Logic (TASK014) ---
        # This is a simplified example. In a real scenario, this would involve a more complex module
        # to construct nuanced prompts based on the combination of inputs.
        prompt_parts = []
        if race: prompt_parts.append(f"a {race} character")
        if anime_name: prompt_parts.append(f"from {anime_name}")
        if age: prompt_parts.append(f"who is {age} years old")
        if gender: prompt_parts.append(f"{gender} character")
        # 不再直接添加identity和personality，而是通过提示词工程模块处理

        base_prompt = "Generate an anime-style character image" # Default prompt
        if prompt_parts:
            base_prompt = f"{base_prompt}: {', '.join(prompt_parts)}"
        
        # 应用性格和身份对提示词的影响
        final_prompt = base_prompt
        
        try:
            # 同时存在性格和身份时，使用组合函数
            if personality and identity:
                final_prompt = combine_personality_identity_image_prompts(base_prompt, personality, identity)
            # 只有性格时
            elif personality:
                final_prompt = apply_personality_to_image_prompt(base_prompt, personality)
            # 只有身份时
            elif identity:
                final_prompt = apply_identity_to_image_prompt(base_prompt, identity)
            else:
                final_prompt = base_prompt + "."
        except ValueError as e:
            # 如果性格或身份无效，回退到基础提示词，并添加原始描述
            if personality and identity:
                final_prompt = f"{base_prompt} with a {personality} personality and {identity} identity."
            elif personality:
                final_prompt = f"{base_prompt} with a {personality} personality."
            elif identity:
                final_prompt = f"{base_prompt} with {identity} identity."
            else:
                final_prompt = base_prompt + "."
        
        # Ensure prompt is within Spark API limits (e.g., 1000 characters)
        # For now, a simple truncation. A real system needs better prompt management.
        if len(final_prompt) > 1000:
            final_prompt = final_prompt[:1000]

        # --- Call Spark Image Generation Service ---
        try:
            # 添加backend-services目录到Python路径
            import sys
            import os
            backend_services_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'backend-services')
            if backend_services_path not in sys.path:
                sys.path.append(backend_services_path)

            from services.spark_image_service import SparkImageService

            # 创建图片生成服务实例
            image_service = SparkImageService()

            # 调用图片生成服务（角色图片，不是背景图片）
            image_url = image_service.generate_and_upload_image(
                prompt=final_prompt,
                width=1024,
                height=1024,
                user_id=None,  # 角色生成时还没有用户ID
                character_id=None  # 角色生成时还没有角色ID
            )

            logger.info(f"图片生成成功: {image_url}")

            return Response({
                "message": "Character generated successfully.",
                "image_url": image_url,
                "generated_prompt": final_prompt
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"图片生成失败: {str(e)}")
            # 如果图片生成失败，返回一个占位符图片（base64格式）
            placeholder_image_url = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAyNCIgaGVpZ2h0PSIxMDI0IiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9IiNjY2NjY2MiLz48dGV4dCB4PSI1MCUiIHk9IjUwJSIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjQ4IiBmaWxsPSIjNjY2NjY2IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+5Zu+54mH55Sf5oiQ5aSx6LSlPC90ZXh0Pjwvc3ZnPg=="

            return Response({
                "message": "Character generated with placeholder image due to service unavailability.",
                "image_url": placeholder_image_url,
                "generated_prompt": final_prompt,
                "warning": "图片生成服务暂时不可用，使用了占位符图片"
            }, status=status.HTTP_200_OK)




class CharacterSaveView(generics.CreateAPIView):
    queryset = Character.objects.all()
    serializer_class = CharacterSaveSerializer
    permission_classes = (IsAuthenticated,)

    def perform_create(self, serializer):
        # Associate the character with the logged-in user
        serializer.save(user=self.request.user)

    def create(self, request, *args, **kwargs):  # type: ignore
        logger.info(f"收到角色保存请求: {request.data}")

        try:
            serializer = self.get_serializer(data=request.data)
            if not serializer.is_valid():
                logger.error(f"角色保存数据验证失败: {serializer.errors}")
                return Response({
                    "message": "数据验证失败",
                    "errors": serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

            self.perform_create(serializer)
            headers = self.get_success_headers(serializer.data)

            # 角色保存成功后，异步生成背景图片
            character_id = serializer.instance.id  # type: ignore
            character_name = serializer.instance.name  # type: ignore

            logger.info(f"角色保存成功: {character_id}")

            # 启动背景图片生成任务
            try:
                from core.services.background_generation_task import background_task_processor
                background_task_processor.generate_backgrounds_async(character_id, count=4)
                logger.info(f"已启动角色 {character_name} 的背景图片生成任务")
            except Exception as e:
                logger.error(f"启动背景图片生成任务失败: {e}")
                # 不影响角色保存的成功响应

            return Response({
                "message": "Character saved successfully.",
                "character_id": character_id,
                "name": character_name,
                "image_url": serializer.instance.image_url,  # type: ignore
                "background_generation": "started"  # 表示背景生成已启动
            }, status=status.HTTP_201_CREATED, headers=headers)

        except Exception as e:
            logger.error(f"角色保存异常: {str(e)}")
            return Response({
                "message": "保存失败",
                "error": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CharacterListView(generics.ListAPIView):
    serializer_class = CharacterListSerializer
    permission_classes = (IsAuthenticated,)

    def get_queryset(self):
        # 返回当前用户的所有角色，按创建时间倒序排列
        return Character.objects.filter(user=self.request.user).order_by('-created_at')


class CharacterDetailView(generics.RetrieveAPIView):
    queryset = Character.objects.all()
    serializer_class = CharacterDetailSerializer
    permission_classes = (IsAuthenticated,)
    lookup_field = 'character_id' # Use character_id from URL as lookup field

    def get_object(self):
        # Retrieve the character by character_id from URL kwargs
        character_id = self.kwargs.get('character_id')
        character = get_object_or_404(Character, id=character_id)

        # Permission check: User can view their own characters or public characters
        if character.user != self.request.user and not character.public:
            # If character is not owned by user and is not public, raise permission denied
            self.permission_denied(self.request)
        return character


class ChatMessageView(APIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = ChatMessageSerializer

    def post(self, request, character_id, *args, **kwargs):  # type: ignore
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        # 获取验证数据（序列化器验证通过后，validated_data不会为空）
        validated_data = serializer.validated_data
        user_message = validated_data.get('user_message', '')  # type: ignore

        # Retrieve the character
        character = get_object_or_404(Character, id=character_id)

        # Permission check: User can only chat with their own character or public characters
        if character.user != request.user and not character.public:
            return Response({
                "detail": "You do not have permission to chat with this character.",
                "code": "PERMISSION_DENIED"
            }, status=status.HTTP_403_FORBIDDEN)

        # --- Preliminary Dialogue AI Logic (TASK019) ---
        # This is a simplified example. In a real scenario, this would involve a complex dialogue AI module
        # considering character's personality, identity, chat history, etc.

        # 基础对话系统提示词
        base_dialogue_prompt = f"You are {character.name}, a virtual character talking to the user {request.user.username}. " \
                              f"You are {character.age} years old."
        
        # 应用性格和身份对对话系统提示词的影响
        try:
            # 同时存在性格和身份时，使用组合函数
            if character.personality and character.identity:
                dialogue_prompt = combine_personality_identity_dialogue_prompts(
                    base_dialogue_prompt, character.personality, character.identity
                )
            # 只有性格时
            elif character.personality:
                dialogue_prompt = apply_personality_to_dialogue_prompt(
                    base_dialogue_prompt, character.personality
                )
            # 只有身份时
            elif character.identity:
                dialogue_prompt = apply_identity_to_dialogue_prompt(
                    base_dialogue_prompt, character.identity
                )
            else:
                dialogue_prompt = base_dialogue_prompt
        except ValueError:
            # 如果性格或身份无效，使用基础提示词
            dialogue_prompt = base_dialogue_prompt
        
        # 获取聊天历史（最近10条消息）
        recent_messages = ChatMessage.objects.filter(
            character=character,
            user=request.user
        ).order_by('-sent_at')[:10]
        
        # 构建聊天历史格式
        chat_history = []
        for msg in reversed(recent_messages):  # 按时间正序
            # 根据发送者类型构建聊天历史
            if msg.sender_type == ChatMessage.SenderType.USER:
                chat_history.append({"role": "user", "content": msg.content})
            elif msg.sender_type == ChatMessage.SenderType.CHARACTER:
                chat_history.append({"role": "assistant", "content": msg.content})
        
        # 调用星火AI对话服务
        try:
            import sys
            import os
            # 添加backend-services目录到Python路径
            backend_services_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'backend-services')
            if backend_services_path not in sys.path:
                sys.path.append(backend_services_path)

            from services.spark_chat_service import spark_chat_service
            
            if spark_chat_service:
                character_response = spark_chat_service.get_dialogue_response(
                    character_prompt=dialogue_prompt,
                    user_message=user_message,
                    chat_history=chat_history
                )
            else:
                character_response = "抱歉，AI服务暂时不可用，请稍后再试。"
                
        except Exception as e:
            logger.error(f"Spark dialogue service error: {str(e)}")
            character_response = "抱歉，处理您的消息时出现了问题，请稍后再试。"

        # 保存用户消息到数据库
        try:
            user_chat_message = ChatMessage.objects.create(
                character=character,
                user=request.user,
                sender_type=ChatMessage.SenderType.USER,
                content=user_message
            )

            # 保存角色回复到数据库
            character_chat_message = ChatMessage.objects.create(
                character=character,
                user=request.user,
                sender_type=ChatMessage.SenderType.CHARACTER,
                content=character_response
            )

            logger.info(f"保存聊天消息成功: 用户消息ID {user_chat_message.id}, 角色回复ID {character_chat_message.id}")

        except Exception as e:
            logger.error(f"保存聊天消息失败: {str(e)}")
            # 即使保存失败，也要返回响应，不影响用户体验

        # TTS语音合成（可选）
        audio_url = None
        enable_tts = request.data.get('enable_tts', False)  # 前端可以控制是否启用TTS

        if enable_tts and character_response:
            try:
                from services.tts_service import tts_service

                if tts_service:
                    # 获取角色的语音设置
                    voice_settings = {}
                    if hasattr(character, 'settings') and character.settings:
                        voice_settings = character.settings.get('voice', {})

                    # 智能选择音色（如果没有设置）
                    voice_type = voice_settings.get('voice_type')
                    if not voice_type:
                        # 根据角色特征自动推荐音色
                        gender = getattr(character, 'gender', 'female')
                        if not gender and hasattr(character, 'appearance_params'):
                            gender = character.appearance_params.get('gender', 'female')

                        voice_type = tts_service.get_voice_by_character_traits(
                            gender=gender,
                            age=character.age,
                            personality=character.personality
                        )

                    # 智能选择情感
                    emotion = voice_settings.get('emotion', 'neutral')
                    if emotion == 'auto' or not emotion:
                        emotion = tts_service.get_emotion_by_context(
                            text=character_response,
                            character_personality=character.personality
                        )

                    # 获取其他语音参数
                    speed = voice_settings.get('speed', 'normal')
                    volume = voice_settings.get('volume', 50)
                    pitch = voice_settings.get('pitch', 50)

                    audio_url = tts_service.synthesize_speech(
                        text=character_response,
                        voice=voice_type,
                        speed=speed,
                        emotion=emotion,
                        volume=volume,
                        pitch=pitch
                    )

                    if audio_url:
                        logger.info(f"TTS语音合成成功: {audio_url} (音色: {voice_type}, 情感: {emotion})")
                    else:
                        logger.warning("TTS语音合成失败")

            except Exception as e:
                logger.error(f"TTS服务调用失败: {str(e)}")
                # TTS失败不影响正常聊天功能

        return Response({
            "message": "Chat message received.",
            "character_response": character_response,
            "audio_url": audio_url,
            "dialogue_prompt": dialogue_prompt  # 仅用于调试，实际生产环境可以移除
        }, status=status.HTTP_200_OK)

class CommunityCharacterListView(generics.ListAPIView):
    serializer_class = CommunityCharacterListSerializer
    # No permission_classes needed as this is for public access

    def get_queryset(self):
        """获取公开角色列表，支持搜索、排序和分页"""
        queryset = Character.objects.filter(public=True, is_deleted=False)

        # 搜索功能
        query = self.request.query_params.get('query', '')
        if query:
            queryset = queryset.filter(
                models.Q(name__icontains=query) |
                models.Q(personality__icontains=query) |
                models.Q(identity__icontains=query) |
                models.Q(readme__icontains=query)
            )

        # 分类筛选
        category = self.request.query_params.get('category', '')
        if category and category != 'all':
            queryset = queryset.filter(category=category)

        # 排序功能
        sort_by = self.request.query_params.get('sortBy', 'latest')
        if sort_by == 'latest':
            queryset = queryset.order_by('-created_at')
        elif sort_by == 'popular':
            queryset = queryset.order_by('-likes_count', '-view_count')
        elif sort_by == 'rating':
            queryset = queryset.order_by('-rating', '-likes_count')
        elif sort_by == 'downloads':
            queryset = queryset.order_by('-downloads', '-likes_count')
        else:
            queryset = queryset.order_by('-created_at')

        return queryset

    def list(self, request, *args, **kwargs):
        """重写list方法以支持统计信息"""
        queryset = self.get_queryset()

        # 分页
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)

        # 添加统计信息
        stats = {
            'total_characters': queryset.count(),
            'total_creators': queryset.values('user').distinct().count(),
            'total_downloads': queryset.aggregate(
                total=models.Sum('downloads')
            )['total'] or 0
        }

        return Response({
            'characters': serializer.data,
            'stats': stats
        })


class AgentIndexView(APIView):
    """Lobe风格的代理索引API"""
    # No permission_classes needed as this is for public access

    def get(self, request, *args, **kwargs):
        """获取代理索引列表"""
        try:
            # 获取公开角色
            queryset = Character.objects.filter(public=True, is_deleted=False)

            # 转换为Lobe格式
            agents = []
            for character in queryset:
                agent_data = {
                    'agentId': str(character.id),
                    'author': character.user.username,
                    'createAt': character.created_at.isoformat(),
                    'greeting': character.greeting or f'你好，我是{character.name}！',
                    'homepage': character.homepage,
                    'meta': {
                        'name': character.name,
                        'description': f'{character.personality} | {character.identity}',
                        'cover': character.image_url or '',
                        'avatar': character.image_url or '',
                        'category': character.category,
                        'gender': character.gender,
                        'readme': character.readme or character.personality
                    },
                    'systemRole': character.system_role or character.personality,
                    'model': character.llm_model,
                    'provider': character.llm_provider,
                    'params': {
                        'temperature': character.llm_temperature,
                        'max_tokens': character.llm_max_tokens
                    }
                }
                agents.append(agent_data)

            return Response({
                'agents': agents
            })

        except Exception as e:
            logger.error(f'获取代理索引失败: {str(e)}')
            return Response({
                'error': '获取代理索引失败',
                'agents': []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AgentDetailView(APIView):
    """Lobe风格的代理详情API"""
    # No permission_classes needed as this is for public access

    def get(self, request, agent_id, *args, **kwargs):
        """获取代理详情"""
        try:
            character = get_object_or_404(
                Character,
                id=agent_id,
                public=True,
                is_deleted=False
            )

            # 增加查看次数
            character.view_count += 1
            character.save(update_fields=['view_count'])

            agent_data = {
                'agentId': str(character.id),
                'author': character.user.username,
                'createAt': character.created_at.isoformat(),
                'greeting': character.greeting or f'你好，我是{character.name}！',
                'homepage': character.homepage,
                'meta': {
                    'name': character.name,
                    'description': f'{character.personality} | {character.identity}',
                    'cover': character.image_url or '',
                    'avatar': character.image_url or '',
                    'category': character.category,
                    'gender': character.gender,
                    'readme': character.readme or character.personality
                },
                'systemRole': character.system_role or character.personality,
                'model': character.llm_model,
                'provider': character.llm_provider,
                'params': {
                    'temperature': character.llm_temperature,
                    'max_tokens': character.llm_max_tokens
                },
                # 额外的统计信息
                'stats': {
                    'likes': character.likes_count,
                    'downloads': character.downloads,
                    'views': character.view_count,
                    'chats': character.chat_count,
                    'rating': character.rating,
                    'isOfficial': character.is_official
                }
            }

            return Response(agent_data)

        except Exception as e:
            logger.error(f'获取代理详情失败: {str(e)}')
            return Response({
                'error': '获取代理详情失败'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class PersonalityListView(APIView):
    """
    提供所有可用的性格列表。
    此API无需认证，以便在角色创建前使用。
    """
    permission_classes = []  # 明确指定不需要认证
    authentication_classes = []  # 覆盖默认的认证类
    
    def get(self, request, *args, **kwargs):  # type: ignore
        """获取所有有效的性格列表"""
        # 构建包含更多详细信息的响应
        personalities = []
        for personality in VALID_PERSONALITIES:
            # TODO: 未来可以从数据库中获取更详细的描述和其他元数据
            personalities.append({
                'name': personality,
                'label': personality  # 保持简单，使用相同的值作为标签
            })
        
        return Response({
            'personalities': personalities,
            'total': len(personalities)
        }, status=status.HTTP_200_OK)


class IdentityListView(APIView):
    """
    提供所有可用的身份列表。
    此API无需认证，以便在角色创建前使用。
    """
    permission_classes = []  # 明确指定不需要认证
    authentication_classes = []  # 覆盖默认的认证类
    
    def get(self, request, *args, **kwargs):  # type: ignore
        """获取所有有效的身份列表"""
        # 构建包含更多详细信息的响应
        identities = []
        for identity in VALID_IDENTITIES:
            # TODO: 未来可以从数据库中获取更详细的描述和其他元数据
            identities.append({
                'name': identity,
                'label': identity  # 保持简单，使用相同的值作为标签
            })
        
        return Response({
            'identities': identities,
            'total': len(identities)
        }, status=status.HTTP_200_OK)


class ImageUploadView(APIView):
    """处理base64图片上传"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        try:
            data = request.data
            base64_image = data.get('image')

            if not base64_image:
                return Response({
                    'error': '缺少图片数据'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 解析base64数据
            if base64_image.startswith('data:image/'):
                # 提取图片格式和数据
                header, image_data = base64_image.split(',', 1)
                image_format = header.split('/')[1].split(';')[0]  # 获取图片格式 (jpeg, png等)
            else:
                return Response({
                    'error': '无效的图片格式'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 解码base64数据
            try:
                image_bytes = base64.b64decode(image_data)
            except Exception as e:
                return Response({
                    'error': f'图片解码失败: {str(e)}'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 生成唯一文件名
            filename = f"{uuid.uuid4()}.{image_format}"

            # 确保media目录存在
            media_root = settings.MEDIA_ROOT
            characters_dir = os.path.join(media_root, 'characters')
            os.makedirs(characters_dir, exist_ok=True)

            # 保存文件
            file_path = os.path.join(characters_dir, filename)
            with open(file_path, 'wb') as f:
                f.write(image_bytes)

            # 生成访问URL
            image_url = f"/media/characters/{filename}"

            return Response({
                'image_url': image_url,
                'message': '图片上传成功'
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"图片上传失败: {str(e)}")
            return Response({
                'error': f'图片上传失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CharacterBackgroundListView(APIView):
    """角色背景图片列表API"""
    permission_classes = [IsAuthenticated]

    def get(self, request, character_id):
        """获取角色的背景图片列表"""
        try:
            # 验证角色是否存在且属于当前用户
            try:
                character = Character.objects.get(id=character_id, user=request.user)
            except Character.DoesNotExist:
                return Response({
                    'code': 404,
                    'message': '角色不存在或无权限访问'
                }, status=status.HTTP_404_NOT_FOUND)

            # 获取查询参数
            status_filter = request.GET.get('status', None)  # 状态筛选
            scene_type_filter = request.GET.get('scene_type', None)  # 场景类型筛选
            page = int(request.GET.get('page', 1))
            page_size = min(int(request.GET.get('page_size', 20)), 50)  # 限制最大页面大小

            # 构建查询
            backgrounds = CharacterBackground.objects.filter(character=character)

            # 应用筛选
            if status_filter:
                backgrounds = backgrounds.filter(generation_status=status_filter)
            if scene_type_filter:
                backgrounds = backgrounds.filter(scene_type=scene_type_filter)

            # 排序
            backgrounds = backgrounds.order_by('-created_at')

            # 分页
            total = backgrounds.count()
            start = (page - 1) * page_size
            end = start + page_size
            backgrounds_page = backgrounds[start:end]

            # 序列化数据
            from core.serializers import CharacterBackgroundSerializer
            serializer = CharacterBackgroundSerializer(backgrounds_page, many=True)

            # 获取生成状态统计
            from core.services.background_generation_task import background_task_processor
            generation_status = background_task_processor.get_generation_status(character_id)

            return Response({
                'code': 0,
                'message': 'success',
                'data': {
                    'backgrounds': serializer.data,
                    'pagination': {
                        'page': page,
                        'page_size': page_size,
                        'total': total,
                        'total_pages': (total + page_size - 1) // page_size
                    },
                    'character_id': character_id,
                    'character_name': character.name,
                    'generation_status': generation_status
                }
            }, status=status.HTTP_200_OK)

        except ValueError as e:
            return Response({
                'code': 400,
                'message': f'参数错误: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"获取背景图片列表失败: {str(e)}")
            return Response({
                'code': 500,
                'message': f'服务器错误: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CharacterBackgroundRetryView(APIView):
    """重试失败的背景图片生成"""
    permission_classes = [IsAuthenticated]

    def post(self, request, character_id):
        """重试角色的失败背景图片生成"""
        try:
            # 验证角色是否存在且属于当前用户
            try:
                character = Character.objects.get(id=character_id, user=request.user)
            except Character.DoesNotExist:
                return Response({
                    'code': 404,
                    'message': '角色不存在或无权限访问'
                }, status=status.HTTP_404_NOT_FOUND)

            # 启动重试任务
            from core.services.background_generation_task import background_task_processor
            background_task_processor.retry_failed_backgrounds(character_id)

            return Response({
                'code': 0,
                'message': '重试任务已启动',
                'data': {
                    'character_id': character_id,
                    'character_name': character.name
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"重试背景图片生成失败: {str(e)}")
            return Response({
                'code': 500,
                'message': f'服务器错误: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
