import os
import requests
import json
import time
import base64
import hashlib
import hmac
from datetime import datetime
from wsgiref.handlers import format_date_time
from time import mktime
from urllib.parse import urlparse, urlencode
from typing import Optional, Tuple, List, Dict, Any, Union
from dotenv import load_dotenv
import logging
import asyncio
import concurrent.futures
from threading import Thread
import base64

# 加载环境变量
load_dotenv()

logger = logging.getLogger(__name__)

class SparkImageService:
    """
    星火图片生成服务，基于官方文档实现
    API地址: https://spark-api.cn-huabei-1.xf-yun.com/v2.1/tti
    """

    # 根据官方文档定义支持的图片分辨率列表
    SUPPORTED_RESOLUTIONS = [
        (512, 512),    # 6图点数
        (640, 360),    # 6图点数
        (640, 480),    # 6图点数
        (640, 640),    # 7图点数
        (680, 512),    # 7图点数
        (512, 680),    # 7图点数
        (768, 768),    # 8图点数
        (720, 1280),   # 12图点数
        (1280, 720),   # 12图点数
        (1024, 1024),  # 14图点数
    ]

    # 重试机制配置
    MAX_RETRIES = 3
    INITIAL_BACKOFF_SECONDS = 1

    def __init__(self):
        self.app_id = os.getenv("SPARK_APP_ID")
        self.api_key = os.getenv("SPARK_API_KEY")
        self.api_secret = os.getenv("SPARK_API_SECRET")
        # 使用官方文档中的正确API地址
        self.image_api_url = "http://spark-api.cn-huabei-1.xf-yun.com/v2.1/tti"

        if not all([self.app_id, self.api_key, self.api_secret]):
            raise ValueError("Spark API credentials (APP_ID, API_KEY, API_SECRET) must be set in environment variables.")

    def _generate_auth_url(self, method="POST"):
        """
        根据示例代码生成鉴权URL（使用URL参数方式）
        """
        parsed_url = urlparse(self.image_api_url)
        host = parsed_url.netloc
        path = parsed_url.path

        # 生成RFC1123格式的时间戳
        now = datetime.now()
        date = format_date_time(mktime(now.timetuple()))

        # 拼接签名字符串
        signature_origin = f"host: {host}\ndate: {date}\n{method} {path} HTTP/1.1"

        # 进行hmac-sha256加密
        api_secret_str = str(self.api_secret)
        signature_sha = hmac.new(
            api_secret_str.encode('utf-8'),
            signature_origin.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()

        signature_sha_base64 = base64.b64encode(signature_sha).decode(encoding='utf-8')

        # 构建authorization字符串
        authorization_origin = f'api_key="{self.api_key}", algorithm="hmac-sha256", headers="host date request-line", signature="{signature_sha_base64}"'
        authorization = base64.b64encode(authorization_origin.encode('utf-8')).decode(encoding='utf-8')

        # 构建URL参数
        values = {
            "host": host,
            "date": date,
            "authorization": authorization
        }

        # 返回带认证参数的URL
        return self.image_api_url + "?" + urlencode(values)

    def _build_request_body(self, prompt: str, width: int = 1024, height: int = 1024):
        """
        根据官方示例代码构建请求体，支持自定义图片尺寸
        """
        return {
            "header": {
                "app_id": self.app_id,
                "uid": "123456789"  # 按照官方示例添加uid字段
            },
            "parameter": {
                "chat": {
                    "domain": "general",  # 按照官方示例使用general
                    "temperature": 0.5,
                    "max_tokens": 4096,
                    "width": width,   # 图片宽度
                    "height": height  # 图片高度
                }
            },
            "payload": {
                "message": {
                    "text": [
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ]
                }
            }
        }

    def generate_and_upload_image(
        self, prompt: str, width: Optional[int] = None, height: Optional[int] = None,
        negative_prompt: Optional[str] = None, user_id: Optional[int] = None, character_id: Optional[int] = None
    ) -> str:
        """
        调用星火图片生成API，根据官方示例实现

        注意：根据官方示例，width和height参数可能不被API支持，
        图片尺寸可能由API默认决定或通过提示词控制

        Args:
            prompt (str): 图片生成提示词，长度不超过1000个字符
            width (int, optional): 图片宽度（可能不被API支持）
            height (int, optional): 图片高度（可能不被API支持）
            negative_prompt (str, optional): 反向提示词（暂不支持）

        Returns:
            str: 生成的图片URL或base64数据

        Raises:
            ValueError: 如果提示词过长
            Exception: 调用API失败时抛出异常
        """
        # 提示词长度校验（根据官方文档限制）
        if len(prompt) > 1000:
            raise ValueError("Prompt length cannot exceed 1000 characters")

        try:
            # 1. 构建请求体（按照官方示例格式）
            # 使用传入的width和height参数，如果没有传入则使用默认值
            actual_width = width if width is not None else 1024
            actual_height = height if height is not None else 1024
            request_body = self._build_request_body(prompt, actual_width, actual_height)

            # 2. 生成鉴权URL（使用URL参数方式）
            auth_url = self._generate_auth_url()

            # 3. 设置请求头（按照官方示例）
            headers = {
                "Content-Type": "application/json"
            }

            # 4. 发送请求（带重试机制）
            for retry_num in range(self.MAX_RETRIES + 1):
                try:
                    logger.info(f"发送图片生成请求，提示词: {prompt[:50]}...")

                    response = requests.post(
                        auth_url,  # 使用带认证参数的URL
                        json=request_body,
                        headers=headers,
                        timeout=60  # 增加超时时间到60秒
                    )
                    response.raise_for_status()

                    # 5. 处理响应
                    response_data = response.json()
                    return self._process_response(response_data, user_id, character_id)

                except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                    if retry_num < self.MAX_RETRIES:
                        delay = self.INITIAL_BACKOFF_SECONDS * (2 ** retry_num)
                        logger.warning(f"网络错误，第 {retry_num + 1}/{self.MAX_RETRIES} 次重试，等待 {delay} 秒...")
                        time.sleep(delay)
                    else:
                        raise Exception(f"网络连接失败，已达到最大重试次数: {e}")

                except requests.exceptions.HTTPError as e:
                    if e.response.status_code == 401:
                        # 401错误通常是认证问题，不需要重试
                        error_msg = f"认证失败 (401): {e.response.text}"
                        if "HMAC signature does not match" in e.response.text:
                            error_msg += "\n可能的原因：\n1. API_KEY或API_SECRET不正确\n2. 时间偏差超过300秒\n3. 签名计算错误"
                        elif "Unauthorized" in e.response.text:
                            error_msg += "\n可能的原因：\n1. 应用未开通图片生成服务\n2. API密钥权限不足\n3. 应用状态异常"
                        raise Exception(error_msg)
                    elif e.response.status_code == 403:
                        # 403错误通常是权限或签名问题
                        error_msg = f"权限不足 (403): {e.response.text}"
                        if "HMAC signature cannot be verified" in e.response.text:
                            error_msg += "\n可能的原因：\n1. 签名格式不正确\n2. 时间戳格式问题\n3. 请求头格式错误"
                        raise Exception(error_msg)
                    elif 500 <= e.response.status_code < 600 and retry_num < self.MAX_RETRIES:
                        delay = self.INITIAL_BACKOFF_SECONDS * (2 ** retry_num)
                        logger.warning(f"服务器错误 ({e.response.status_code})，第 {retry_num + 1}/{self.MAX_RETRIES} 次重试...")
                        time.sleep(delay)
                    else:
                        raise Exception(f"HTTP请求失败: {e.response.status_code} - {e.response.text}")

            raise Exception("所有重试尝试都已用尽")

        except Exception as e:
            logger.error(f"图片生成失败: {e}")
            raise Exception(f"图片生成失败: {e}")

    def _process_response(self, response_data: dict, user_id: Optional[int] = None, character_id: Optional[int] = None) -> str:
        """
        处理API响应，上传图片到云存储并返回URL
        """
        try:
            header = response_data.get("header", {})
            code = header.get("code", -1)

            if code != 0:
                message = header.get("message", "Unknown error")
                raise Exception(f"API返回错误: code={code}, message={message}")

            # 提取base64图片数据
            payload = response_data.get("payload", {})
            choices = payload.get("choices", {})
            text_list = choices.get("text", [])

            if not text_list:
                raise Exception("API响应中没有图片数据")

            image_content = text_list[0].get("content", "")
            if not image_content:
                raise Exception("API响应中图片内容为空")

            # 解码base64图片数据
            try:
                image_bytes = base64.b64decode(image_content)
            except Exception as e:
                raise Exception(f"base64解码失败: {e}")

            # 优先使用本地存储
            try:
                from .local_storage_service import LocalStorageService
                storage_service = LocalStorageService()

                # 判断是否为背景图片（如果有character_id或user_id，通常是背景图片）
                is_background = character_id is not None or user_id is not None

                # 上传图片到本地存储并获取URL
                image_url = storage_service.upload_image_data(
                    image_data=image_bytes,
                    user_id=user_id,
                    character_id=character_id,
                    file_extension='.jpg',
                    is_background=is_background
                )

                logger.info(f"图片本地存储成功: {image_url}")
                return image_url

            except Exception as e:
                logger.warning(f"本地存储失败，尝试云存储: {e}")

                # 如果本地存储失败，尝试云存储
                try:
                    from .file_storage_service import FileStorageService
                    cloud_storage = FileStorageService()

                    image_url = cloud_storage.upload_image_data(
                        image_data=image_bytes,
                        user_id=user_id,
                        character_id=character_id,
                        file_extension='.jpg'
                    )

                    logger.info(f"图片云存储成功: {image_url}")
                    return image_url

                except Exception as cloud_error:
                    logger.warning(f"云存储也失败，返回base64格式: {cloud_error}")
                    # 如果都失败，返回base64数据作为最后备选方案
                    return f"data:image/jpeg;base64,{image_content}"

        except Exception as e:
            logger.error(f"响应处理失败: {e}")
            raise Exception(f"响应处理失败: {e}")

    def generate_background_image(self, prompt: str, character_id: Optional[int] = None, user_id: Optional[int] = None, width: int = 1280, height: int = 720) -> str:
        """
        生成单张背景图片

        Args:
            prompt: 背景场景提示词
            character_id: 角色ID（用于文件存储路径）
            user_id: 用户ID（用于文件存储路径）
            width: 图片宽度
            height: 图片高度

        Returns:
            图片URL或base64数据
        """
        logger.info(f"开始生成背景图片: {prompt[:50]}...")

        try:
            # 调用现有的图片生成方法，传递用户ID和角色ID
            image_url = self.generate_and_upload_image(
                prompt=prompt,
                width=width,
                height=height,
                user_id=user_id,
                character_id=character_id
            )
            logger.info(f"背景图片生成成功: {image_url}")
            return image_url
        except Exception as e:
            logger.error(f"背景图片生成失败: {e}")
            raise e

    def generate_multiple_backgrounds(self, prompts: List[str], character_id: Optional[int] = None, user_id: Optional[int] = None, width: int = 1280, height: int = 720) -> List[Dict[str, Any]]:
        """
        批量生成多张背景图片

        Args:
            prompts: 提示词列表
            width: 图片宽度
            height: 图片高度

        Returns:
            生成结果列表，每个元素包含 {'prompt': str, 'image_url': str, 'status': str, 'error': str}
        """
        logger.info(f"开始批量生成 {len(prompts)} 张背景图片")

        results = []

        # 使用线程池进行并发生成，但限制并发数量避免API限制
        max_workers = min(3, len(prompts))  # 最多3个并发请求

        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_prompt = {}
            for i, prompt in enumerate(prompts):
                future = executor.submit(self._generate_single_background_safe, prompt, character_id, user_id, width, height, i)
                future_to_prompt[future] = prompt

            # 收集结果
            for future in concurrent.futures.as_completed(future_to_prompt):
                prompt = future_to_prompt[future]
                try:
                    result = future.result()
                    results.append(result)
                    logger.info(f"背景图片生成完成: {result['status']}")
                except Exception as e:
                    logger.error(f"背景图片生成异常: {e}")
                    results.append({
                        'prompt': prompt,
                        'image_url': None,
                        'status': 'failed',
                        'error': str(e)
                    })

        # 按原始顺序排序结果
        results.sort(key=lambda x: x.get('index', 0))

        success_count = sum(1 for r in results if r['status'] == 'success')
        logger.info(f"批量生成完成: {success_count}/{len(prompts)} 成功")

        return results

    def _generate_single_background_safe(self, prompt: str, character_id: Optional[int], user_id: Optional[int], width: int, height: int, index: int) -> Dict[str, Any]:
        """
        安全地生成单张背景图片（用于并发调用）

        Args:
            prompt: 提示词
            character_id: 角色ID
            user_id: 用户ID
            width: 图片宽度
            height: 图片高度
            index: 索引（用于排序）

        Returns:
            生成结果字典
        """
        try:
            # 添加延迟避免API频率限制
            if index > 0:
                time.sleep(index * 2)  # 每个请求间隔2秒

            image_url = self.generate_background_image(prompt, character_id, user_id, width, height)
            return {
                'prompt': prompt,
                'image_url': image_url,
                'status': 'success',
                'error': None,
                'index': index
            }
        except Exception as e:
            return {
                'prompt': prompt,
                'image_url': None,
                'status': 'failed',
                'error': str(e),
                'index': index
            }