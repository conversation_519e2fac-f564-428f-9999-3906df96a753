from django.contrib.auth.models import User
from django.db import transaction, IntegrityError
from django.utils import timezone
from django.contrib.auth.hashers import make_password
from .models import UserProfile, Character, ChatMessage
from .validators import DataValidator
from typing import Dict, List, Optional, Union, Any, TYPE_CHECKING
import logging
from django.core.paginator import Paginator

if TYPE_CHECKING:
    # 为类型检查添加User的profile属性
    class UserWithProfile(User):
        profile: UserProfile

logger = logging.getLogger(__name__)

class UserDataAccess:
    """用户数据访问层，提供用户相关的CRUD操作"""
    
    @staticmethod
    def create_user(username: str, email: str, password: str, **extra_fields) -> Optional[User]:
        """
        创建新用户
        
        Args:
            username: 用户名
            email: 电子邮件
            password: 密码（将被哈希处理）
            **extra_fields: 其他字段
            
        Returns:
            User: 创建成功返回用户对象，失败返回None
        """
        try:
            # 验证和清洗数据
            user_data = {
                'username': username,
                'email': email,
                'password': password,
                **extra_fields
            }
            is_valid, cleaned_data, errors = DataValidator.validate_user_data(user_data)
            
            if not is_valid:
                logger.error(f"创建用户数据验证失败: {errors}")
                return None
            
            with transaction.atomic():
                # 创建用户时自动对密码进行哈希处理
                user = User.objects.create(
                    username=cleaned_data['username'],
                    email=cleaned_data['email'],
                    password=make_password(cleaned_data['password'])
                )
                
                # 更新其他字段
                for key, value in cleaned_data.items():
                    if key not in ['username', 'email', 'password'] and hasattr(user, key):
                        setattr(user, key, value)
                
                user.save()
                # UserProfile通过信号自动创建
                return user
        except IntegrityError as e:
            logger.error(f"创建用户失败: {str(e)}")
            return None
    
    @staticmethod
    def get_user_by_id(user_id: int) -> Optional[User]:
        """
        通过ID获取用户
        
        Args:
            user_id: 用户ID
            
        Returns:
            User: 找到返回用户对象，未找到返回None
        """
        try:
            user = User.objects.get(id=user_id)
            # 检查用户是否被软删除
            if hasattr(user, 'profile') and getattr(user, 'profile').is_deleted:
                return None
            return user
        except User.DoesNotExist:
            return None
    
    @staticmethod
    def get_user_by_username(username: str) -> Optional[User]:
        """
        通过用户名获取用户
        
        Args:
            username: 用户名
            
        Returns:
            User: 找到返回用户对象，未找到返回None
        """
        try:
            # 清洗用户名
            username = DataValidator.sanitize_string(username)
            if not username:
                return None
                
            user = User.objects.get(username=username)
            # 检查用户是否被软删除
            if hasattr(user, 'profile') and getattr(user, 'profile').is_deleted:
                return None
            return user
        except User.DoesNotExist:
            return None
    
    @staticmethod
    def get_user_by_email(email: str) -> Optional[User]:
        """
        通过电子邮件获取用户
        
        Args:
            email: 电子邮件
            
        Returns:
            User: 找到返回用户对象，未找到返回None
        """
        try:
            # 清洗邮箱
            email = DataValidator.sanitize_string(email).lower()
            if not email:
                return None
                
            user = User.objects.get(email=email)
            # 检查用户是否被软删除
            if hasattr(user, 'profile') and getattr(user, 'profile').is_deleted:
                return None
            return user
        except User.DoesNotExist:
            return None
    
    @staticmethod
    def update_user(user_id: int, **update_fields) -> Optional[User]:
        """
        更新用户信息
        
        Args:
            user_id: 用户ID
            **update_fields: 要更新的字段
            
        Returns:
            User: 更新成功返回用户对象，失败返回None
        """
        try:
            # 验证和清洗数据
            is_valid, cleaned_data, errors = DataValidator.validate_user_data(update_fields)
            
            if not is_valid:
                logger.error(f"更新用户数据验证失败: {errors}")
                return None
            
            with transaction.atomic():
                user = User.objects.get(id=user_id)
                
                # 处理密码更新，确保密码被哈希
                if 'password' in cleaned_data:
                    cleaned_data['password'] = make_password(cleaned_data['password'])
                
                # 更新用户属性
                for key, value in cleaned_data.items():
                    if hasattr(user, key):
                        setattr(user, key, value)
                
                user.save()
                return user
        except User.DoesNotExist:
            logger.error(f"更新用户失败: 用户ID {user_id} 不存在")
            return None
        except IntegrityError as e:
            logger.error(f"更新用户失败: {str(e)}")
            return None
    
    @staticmethod
    def delete_user(user_id: int) -> bool:
        """
        软删除用户
        
        Args:
            user_id: 用户ID
            
        Returns:
            bool: 删除成功返回True，失败返回False
        """
        try:
            with transaction.atomic():
                user = User.objects.get(id=user_id)
                if hasattr(user, 'profile'):
                    # 执行软删除
                    getattr(user, 'profile').soft_delete()
                    return True
                return False
        except User.DoesNotExist:
            logger.error(f"删除用户失败: 用户ID {user_id} 不存在")
            return False
    
    @staticmethod
    def restore_user(user_id: int) -> bool:
        """
        恢复软删除的用户
        
        Args:
            user_id: 用户ID
            
        Returns:
            bool: 恢复成功返回True，失败返回False
        """
        try:
            with transaction.atomic():
                user = User.objects.get(id=user_id)
                if hasattr(user, 'profile'):
                    # 恢复软删除的用户
                    getattr(user, 'profile').restore()
                    return True
                return False
        except User.DoesNotExist:
            logger.error(f"恢复用户失败: 用户ID {user_id} 不存在")
            return False


class CharacterDataAccess:
    """角色数据访问层，提供角色相关的CRUD操作"""
    
    @staticmethod
    def create_character(
        user_id: int, 
        name: str, 
        age: int, 
        identity: str, 
        personality: str,
        image_url: Optional[str] = None,
        appearance_params: Optional[Dict] = None,
        settings: Optional[Dict] = None,
        public: bool = False
    ) -> Optional[Character]:
        """
        创建新角色
        
        Args:
            user_id: 用户ID
            name: 角色名称
            age: 角色年龄
            identity: 角色身份
            personality: 角色性格
            image_url: 角色图像URL
            appearance_params: 角色外观参数 (JSON)
            settings: 角色设置 (JSON)
            public: 是否公开
            
        Returns:
            Character: 创建成功返回角色对象，失败返回None
        """
        try:
            # 验证和清洗数据
            character_data = {
                'name': name,
                'age': age,
                'identity': identity,
                'personality': personality,
                'image_url': image_url,
                'appearance_params': appearance_params or {},
                'settings': settings or {},
                'public': public
            }
            is_valid, cleaned_data, errors = DataValidator.validate_character_data(character_data)
            
            if not is_valid:
                logger.error(f"创建角色数据验证失败: {errors}")
                return None
            
            with transaction.atomic():
                user = User.objects.get(id=user_id)
                
                character = Character.objects.create(
                    user=user,
                    name=cleaned_data.get('name', name),
                    age=cleaned_data.get('age', age),
                    identity=cleaned_data.get('identity', identity),
                    personality=cleaned_data.get('personality', personality),
                    image_url=cleaned_data.get('image_url', image_url),
                    appearance_params=cleaned_data.get('appearance_params', {}),
                    settings=cleaned_data.get('settings', {}),
                    public=cleaned_data.get('public', public)
                )
                return character
        except User.DoesNotExist:
            logger.error(f"创建角色失败: 用户ID {user_id} 不存在")
            return None
        except IntegrityError as e:
            logger.error(f"创建角色失败: {str(e)}")
            return None
    
    @staticmethod
    def get_character_by_id(character_id: int, include_deleted: bool = False) -> Optional[Character]:
        """
        通过ID获取角色
        
        Args:
            character_id: 角色ID
            include_deleted: 是否包含已删除的角色
            
        Returns:
            Character: 找到返回角色对象，未找到返回None
        """
        try:
            character = Character.objects.get(id=character_id)
            # 检查角色是否被软删除
            if not include_deleted and character.is_deleted:
                return None
            return character
        except Character.DoesNotExist:
            return None
    
    @staticmethod
    def get_characters_by_user(
        user_id: int, 
        include_private: bool = True, 
        include_deleted: bool = False
    ) -> List[Character]:
        """
        获取用户的所有角色
        
        Args:
            user_id: 用户ID
            include_private: 是否包含私有角色
            include_deleted: 是否包含已删除的角色
            
        Returns:
            List[Character]: 角色列表
        """
        try:
            query = Character.objects.filter(user_id=user_id)
            
            # 是否包含已删除的角色
            if not include_deleted:
                query = query.filter(is_deleted=False)
            
            # 是否包含私有角色
            if not include_private:
                query = query.filter(public=True)
            
            return list(query)
        except Exception as e:
            logger.error(f"获取用户角色失败: {str(e)}")
            return []
    
    @staticmethod
    def get_public_community_characters(
        page: int = 1, 
        page_size: int = 10
    ) -> Dict[str, Any]:
        """
        获取社区公开的角色（分页）
        
        Args:
            page: 页码（从1开始）
            page_size: 每页数量
            
        Returns:
            Dict: 包含分页信息和角色列表
        """
        try:
            # 清洗分页参数
            page = DataValidator.sanitize_integer(page, default=1, min_value=1)
            page_size = DataValidator.sanitize_integer(page_size, default=10, min_value=1, max_value=100)
            
            # 获取未删除的公开角色
            query = Character.objects.filter(
                public=True,
                is_deleted=False
            ).order_by('-created_at')  # 按创建时间倒序
            
            # 分页
            paginator = Paginator(query, page_size)
            page_obj = paginator.get_page(page)
            
            return {
                'total': paginator.count,
                'total_pages': paginator.num_pages,
                'current_page': page,
                'page_size': page_size,
                'results': list(page_obj.object_list)
            }
        except Exception as e:
            logger.error(f"获取社区角色失败: {str(e)}")
            return {
                'total': 0,
                'total_pages': 0,
                'current_page': page,
                'page_size': page_size,
                'results': []
            }
    
    @staticmethod
    def update_character(character_id: int, **update_fields) -> Optional[Character]:
        """
        更新角色信息
        
        Args:
            character_id: 角色ID
            **update_fields: 要更新的字段
            
        Returns:
            Character: 更新成功返回角色对象，失败返回None
        """
        try:
            # 验证和清洗数据
            is_valid, cleaned_data, errors = DataValidator.validate_character_data(update_fields)
            
            if not is_valid:
                logger.error(f"更新角色数据验证失败: {errors}")
                return None
            
            with transaction.atomic():
                character = Character.objects.get(id=character_id)
                
                # 处理JSON字段的更新
                if 'appearance_params' in cleaned_data and isinstance(cleaned_data['appearance_params'], dict):
                    if character.appearance_params is not None:
                        character.appearance_params.update(cleaned_data.pop('appearance_params'))
                    else:
                        character.appearance_params = cleaned_data.pop('appearance_params')

                if 'settings' in cleaned_data and isinstance(cleaned_data['settings'], dict):
                    if character.settings is not None:
                        character.settings.update(cleaned_data.pop('settings'))
                    else:
                        character.settings = cleaned_data.pop('settings')
                
                # 更新其他字段
                for key, value in cleaned_data.items():
                    if hasattr(character, key):
                        setattr(character, key, value)
                
                character.save()
                return character
        except Character.DoesNotExist:
            logger.error(f"更新角色失败: 角色ID {character_id} 不存在")
            return None
        except IntegrityError as e:
            logger.error(f"更新角色失败: {str(e)}")
            return None
    
    @staticmethod
    def delete_character(character_id: int) -> bool:
        """
        软删除角色
        
        Args:
            character_id: 角色ID
            
        Returns:
            bool: 删除成功返回True，失败返回False
        """
        try:
            with transaction.atomic():
                character = Character.objects.get(id=character_id)
                # 执行软删除
                character.soft_delete()
                return True
        except Character.DoesNotExist:
            logger.error(f"删除角色失败: 角色ID {character_id} 不存在")
            return False
    
    @staticmethod
    def restore_character(character_id: int) -> bool:
        """
        恢复软删除的角色
        
        Args:
            character_id: 角色ID
            
        Returns:
            bool: 恢复成功返回True，失败返回False
        """
        try:
            with transaction.atomic():
                character = Character.objects.get(id=character_id)
                # 恢复软删除的角色
                character.restore()
                return True
        except Character.DoesNotExist:
            logger.error(f"恢复角色失败: 角色ID {character_id} 不存在")
            return False


class ChatMessageDataAccess:
    """聊天消息数据访问层，提供聊天消息相关的CRUD操作"""
    
    @staticmethod
    def create_message(
        character_id: int,
        user_id: int,
        content: str,
        sender_type: str  # 'user' 或 'character'
    ) -> Optional[ChatMessage]:
        """
        创建新聊天消息
        
        Args:
            character_id: 角色ID
            user_id: 用户ID
            content: 消息内容
            sender_type: 发送者类型 ('user' 或 'character')
            
        Returns:
            ChatMessage: 创建成功返回消息对象，失败返回None
        """
        try:
            # 验证和清洗数据
            message_data = {
                'content': content,
                'sender_type': sender_type
            }
            is_valid, cleaned_data, errors = DataValidator.validate_chat_message_data(message_data)
            
            if not is_valid:
                logger.error(f"创建消息数据验证失败: {errors}")
                return None
            
            with transaction.atomic():
                # 验证发送者类型
                if cleaned_data['sender_type'] not in [ChatMessage.SenderType.USER, ChatMessage.SenderType.CHARACTER]:
                    raise ValueError(f"无效的发送者类型: {cleaned_data['sender_type']}")
                
                # 获取角色和用户
                character = Character.objects.get(id=character_id)
                user = User.objects.get(id=user_id)
                
                # 创建消息
                message = ChatMessage.objects.create(
                    character=character,
                    user=user,
                    content=cleaned_data['content'],
                    sender_type=cleaned_data['sender_type']
                )
                return message
        except (Character.DoesNotExist, User.DoesNotExist) as e:
            logger.error(f"创建消息失败: {str(e)}")
            return None
        except IntegrityError as e:
            logger.error(f"创建消息失败: {str(e)}")
            return None
        except ValueError as e:
            logger.error(f"创建消息失败: {str(e)}")
            return None
    
    @staticmethod
    def get_message_by_id(message_id: int) -> Optional[ChatMessage]:
        """
        通过ID获取消息
        
        Args:
            message_id: 消息ID
            
        Returns:
            ChatMessage: 找到返回消息对象，未找到返回None
        """
        try:
            return ChatMessage.objects.get(id=message_id)
        except ChatMessage.DoesNotExist:
            return None
    
    @staticmethod
    def get_conversation(
        character_id: int,
        user_id: int,
        page: int = 1,
        page_size: int = 20
    ) -> Dict[str, Any]:
        """
        获取用户和角色之间的对话记录（分页）
        
        Args:
            character_id: 角色ID
            user_id: 用户ID
            page: 页码（从1开始）
            page_size: 每页数量
            
        Returns:
            Dict: 包含分页信息和消息列表
        """
        try:
            # 清洗分页参数
            page = DataValidator.sanitize_integer(page, default=1, min_value=1)
            page_size = DataValidator.sanitize_integer(page_size, default=20, min_value=1, max_value=100)
            
            # 获取用户和角色之间的对话
            query = ChatMessage.objects.filter(
                character_id=character_id,
                user_id=user_id
            ).order_by('-sent_at')  # 按发送时间倒序
            
            # 分页
            paginator = Paginator(query, page_size)
            page_obj = paginator.get_page(page)
            
            # 转换为列表并按时间正序排列
            messages = list(page_obj.object_list)
            messages.reverse()  # 由于查询时是倒序，这里需要反转回来
            
            return {
                'total': paginator.count,
                'total_pages': paginator.num_pages,
                'current_page': page,
                'page_size': page_size,
                'results': messages
            }
        except Exception as e:
            logger.error(f"获取对话记录失败: {str(e)}")
            return {
                'total': 0,
                'total_pages': 0,
                'current_page': page,
                'page_size': page_size,
                'results': []
            }
    
    @staticmethod
    def get_latest_conversation(
        character_id: int,
        user_id: int,
        limit: int = 10
    ) -> List[ChatMessage]:
        """
        获取用户和角色之间的最新对话记录
        
        Args:
            character_id: 角色ID
            user_id: 用户ID
            limit: 返回的消息数量
            
        Returns:
            List[ChatMessage]: 消息列表
        """
        try:
            # 清洗参数
            limit = DataValidator.sanitize_integer(limit, default=10, min_value=1, max_value=100)
            
            # 获取最新的对话记录
            messages = ChatMessage.objects.filter(
                character_id=character_id,
                user_id=user_id
            ).order_by('-sent_at')[:limit]  # 按发送时间倒序，取最新的limit条
            
            # 转换为列表并按时间正序排列
            result = list(messages)
            result.reverse()
            
            return result
        except Exception as e:
            logger.error(f"获取最新对话记录失败: {str(e)}")
            return []
    
    @staticmethod
    def delete_message(message_id: int) -> bool:
        """
        删除消息
        
        Args:
            message_id: 消息ID
            
        Returns:
            bool: 删除成功返回True，失败返回False
        """
        try:
            with transaction.atomic():
                message = ChatMessage.objects.get(id=message_id)
                message.delete()
                return True
        except ChatMessage.DoesNotExist:
            logger.error(f"删除消息失败: 消息ID {message_id} 不存在")
            return False 