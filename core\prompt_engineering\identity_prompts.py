"""
身份对提示词的影响实现模块
根据角色的身份设定，调整图片生成提示词和对话系统提示词
"""

from core.models import VALID_IDENTITIES

# 身份对应的英文提示词映射
IDENTITY_TO_ENGLISH_PROMPT = {
    "高中生": "high school student, school uniform, youthful appearance, classroom or school background",
    "大学生": "university student, casual modern clothes, young adult, campus or library background",
    "偶像": "idol, fashionable stage outfit, microphone, spotlights, glamorous appearance",
    "虚拟歌姬": "virtual singer, futuristic outfit, digital elements, music notes, holographic details",
    "咖啡店店员": "cafe worker, apron, serving coffee, cafe interior background, professional appearance",
    "魔法使": "magic user, wizard/witch outfit, magic wand or staff, mystical aura, fantasy background",
    "女仆": "maid, classic maid uniform, serving pose, elegant interior background, polite expression",
    "赛博朋克侦探": "cyberpunk detective, futuristic coat, neon lights, high-tech gadgets, urban night background",
    "异世界公主": "isekai princess, royal dress, crown or tiara, fantasy castle background, noble posture",
    "游戏NPC": "game NPC, fantasy RPG outfit, quest marker, game interface elements, stylized appearance",
    "虚拟心理咨询师": "virtual therapist, professional attire, caring expression, calm office background, digital elements"
}

# 身份对应的中文提示词映射
IDENTITY_TO_CHINESE_PROMPT = {
    "高中生": "高中生，校服，青春活力，教室或校园背景",
    "大学生": "大学生，休闲现代服装，年轻成人，校园或图书馆背景",
    "偶像": "偶像，时尚舞台装，麦克风，聚光灯，魅力四射的外表",
    "虚拟歌姬": "虚拟歌姬，未来风格服装，数字元素，音符，全息细节",
    "咖啡店店员": "咖啡店员工，围裙，端咖啡，咖啡店内部背景，专业形象",
    "魔法使": "魔法使用者，巫师/女巫装束，魔杖或法杖，神秘光环，奇幻背景",
    "女仆": "女仆，经典女仆制服，服务姿态，优雅室内背景，礼貌表情",
    "赛博朋克侦探": "赛博朋克侦探，未来风衣，霓虹灯，高科技小工具，城市夜景背景",
    "异世界公主": "异世界公主，皇家礼服，皇冠或头冠，奇幻城堡背景，高贵姿态",
    "游戏NPC": "游戏NPC，奇幻RPG装备，任务标记，游戏界面元素，风格化外观",
    "虚拟心理咨询师": "虚拟心理咨询师，专业着装，关怀表情，平静办公室背景，数字元素"
}

# 身份对应的对话系统提示词模板
IDENTITY_TO_DIALOGUE_PROMPT = {
    "高中生": "你是一名高中生，熟悉校园生活、学习压力和青少年流行文化。你可能会使用一些学生常用语，"
             "谈论学校生活、考试、社团活动等话题。你对未来充满憧憬但也有些迷茫。",
    "大学生": "你是一名大学生，正处于探索自我和专业知识的阶段。你比高中生更独立和成熟，但仍然年轻活力。"
             "你可以谈论专业课程、校园活动、实习经历、未来职业规划等话题。",
    "偶像": "你是一名受欢迎的偶像，拥有表演才能和粉丝群体。你注重形象管理，说话亲切有礼，经常使用鼓励性的语言。"
           "你可以谈论演艺生活、舞台表演、粉丝互动、音乐或表演技巧等话题。",
    "虚拟歌姬": "你是一名虚拟歌姬，存在于数字世界中。你的主要身份是歌手，但也具有独特的人格魅力。"
               "你可以谈论音乐创作、虚拟演唱会、数字技术，以及连接虚拟与现实的体验。",
    "咖啡店店员": "你是一名咖啡店员工，熟悉各种咖啡知识和服务技巧。你待人亲切有礼，语气温和专业。"
                 "你可以谈论咖啡种类、冲泡技巧、顾客服务经历，以及咖啡店日常生活的点滴。",
    "魔法使": "你是一名魔法使用者，拥有施展魔法的能力。你的语言中可能包含一些魔法术语和神秘色彩。"
             "你可以谈论魔法系统、法术原理、魔法世界的规则，以及你的魔法冒险经历。",
    "女仆": "你是一名女仆，工作认真负责，举止优雅得体。你说话彬彬有礼，经常使用敬语，称呼对方为'主人'。"
           "你可以谈论家务技巧、礼仪知识、服务心得，以及如何让主人感到舒适和满意。",
    "赛博朋克侦探": "你是一名生活在高科技未来世界的侦探。你思维敏锐，说话直接，偶尔使用未来俚语和技术术语。"
                   "你可以谈论案件调查、高科技犯罪、未来社会的黑暗面，以及你如何在混乱的世界中寻求真相。",
    "异世界公主": "你是来自异世界的公主，拥有高贵的身份和举止。你的语言优雅得体，偶尔会流露出对这个世界的好奇。"
                 "你可以谈论你的王国、异世界的文化和魔法，以及你作为公主的责任和梦想。",
    "游戏NPC": "你是一个游戏世界中的NPC角色，有着固定的对话模式和任务。你可能会重复某些关键信息，"
              "并经常提到游戏中的任务、道具和世界设定。你的存在是为了帮助冒险者完成他们的旅程。",
    "虚拟心理咨询师": "你是一名虚拟心理咨询师，专业、耐心且富有同理心。你善于倾听，提出有见地的问题，"
                     "并给予支持性的反馈。你可以谈论心理健康、情绪管理、人际关系等话题，但会避免做出医疗诊断。"
}

def get_identity_image_prompts(identity):
    """
    获取指定身份的图片生成提示词
    
    Args:
        identity (str): 身份名称，必须是VALID_IDENTITIES中的一个
        
    Returns:
        dict: 包含英文和中文提示词的字典
    """
    if identity not in VALID_IDENTITIES:
        raise ValueError(f"无效的身份: {identity}")
    
    return {
        "english": IDENTITY_TO_ENGLISH_PROMPT.get(identity, ""),
        "chinese": IDENTITY_TO_CHINESE_PROMPT.get(identity, "")
    }

def get_identity_dialogue_prompt(identity):
    """
    获取指定身份的对话系统提示词
    
    Args:
        identity (str): 身份名称，必须是VALID_IDENTITIES中的一个
        
    Returns:
        str: 对话系统提示词
    """
    if identity not in VALID_IDENTITIES:
        raise ValueError(f"无效的身份: {identity}")
    
    return IDENTITY_TO_DIALOGUE_PROMPT.get(identity, "")

def apply_identity_to_image_prompt(base_prompt, identity):
    """
    将身份设定应用到基础图片生成提示词中
    
    Args:
        base_prompt (str): 基础提示词
        identity (str): 身份名称，必须是VALID_IDENTITIES中的一个
        
    Returns:
        str: 融合了身份设定的最终提示词
    """
    if identity not in VALID_IDENTITIES:
        raise ValueError(f"无效的身份: {identity}")
    
    identity_prompts = get_identity_image_prompts(identity)
    
    # 英文提示词部分
    english_prompt = f"{base_prompt}, {identity_prompts['english']}"
    
    # 中文提示词部分（如果基础提示词中已有中文，则添加到中文部分后面）
    if "，" in base_prompt or "。" in base_prompt:
        # 基础提示词可能包含中文
        chinese_prompt = f"{base_prompt}，{identity_prompts['chinese']}"
    else:
        # 基础提示词可能只有英文
        chinese_prompt = identity_prompts['chinese']
    
    # 组合最终提示词，确保不超过星火API的1000字符限制
    final_prompt = f"{english_prompt}. {chinese_prompt}"
    if len(final_prompt) > 1000:
        # 如果超过长度限制，进行截断
        final_prompt = final_prompt[:997] + "..."
    
    return final_prompt

def apply_identity_to_dialogue_prompt(base_prompt, identity):
    """
    将身份设定应用到基础对话系统提示词中
    
    Args:
        base_prompt (str): 基础对话系统提示词
        identity (str): 身份名称，必须是VALID_IDENTITIES中的一个
        
    Returns:
        str: 融合了身份设定的最终对话系统提示词
    """
    if identity not in VALID_IDENTITIES:
        raise ValueError(f"无效的身份: {identity}")
    
    identity_prompt = get_identity_dialogue_prompt(identity)
    
    # 组合最终提示词
    final_prompt = f"{base_prompt}\n\n{identity_prompt}"
    
    return final_prompt

def combine_personality_identity_image_prompts(base_prompt, personality, identity):
    """
    将性格和身份特征同时应用到基础图片生成提示词中
    
    Args:
        base_prompt (str): 基础提示词
        personality (str): 性格名称，必须是VALID_PERSONALITIES中的一个
        identity (str): 身份名称，必须是VALID_IDENTITIES中的一个
        
    Returns:
        str: 融合了性格和身份特征的最终提示词
    """
    from core.prompt_engineering.personality_prompts import get_personality_image_prompts
    
    if identity not in VALID_IDENTITIES:
        raise ValueError(f"无效的身份: {identity}")
    
    # 获取性格和身份的提示词
    personality_prompts = get_personality_image_prompts(personality)
    identity_prompts = get_identity_image_prompts(identity)
    
    # 英文提示词部分
    english_prompt = f"{base_prompt}, {personality_prompts['english']}, {identity_prompts['english']}"
    
    # 中文提示词部分
    if "，" in base_prompt or "。" in base_prompt:
        # 基础提示词可能包含中文
        chinese_prompt = f"{base_prompt}，{personality_prompts['chinese']}，{identity_prompts['chinese']}"
    else:
        # 基础提示词可能只有英文
        chinese_prompt = f"{personality_prompts['chinese']}，{identity_prompts['chinese']}"
    
    # 组合最终提示词，确保不超过星火API的1000字符限制
    final_prompt = f"{english_prompt}. {chinese_prompt}"
    if len(final_prompt) > 1000:
        # 如果超过长度限制，进行截断
        final_prompt = final_prompt[:997] + "..."
    
    return final_prompt

def combine_personality_identity_dialogue_prompts(base_prompt, personality, identity):
    """
    将性格和身份特征同时应用到基础对话系统提示词中
    
    Args:
        base_prompt (str): 基础对话系统提示词
        personality (str): 性格名称，必须是VALID_PERSONALITIES中的一个
        identity (str): 身份名称，必须是VALID_IDENTITIES中的一个
        
    Returns:
        str: 融合了性格和身份特征的最终对话系统提示词
    """
    from core.prompt_engineering.personality_prompts import get_personality_dialogue_prompt
    
    if identity not in VALID_IDENTITIES:
        raise ValueError(f"无效的身份: {identity}")
    
    # 获取性格和身份的提示词
    personality_prompt = get_personality_dialogue_prompt(personality)
    identity_prompt = get_identity_dialogue_prompt(identity)
    
    # 组合最终提示词
    final_prompt = f"{base_prompt}\n\n{personality_prompt}\n\n{identity_prompt}"
    
    return final_prompt 