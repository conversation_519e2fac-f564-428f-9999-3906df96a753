# Generated by Django 5.2.1 on 2025-06-05 12:37

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ChatMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sender_type', models.CharField(choices=[('user', '用户'), ('character', '角色')], max_length=10)),
                ('content', models.TextField()),
                ('sent_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['sent_at'],
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_deleted', models.BooleanField(default=False)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
            ],
        ),
        migrations.AddField(
            model_name='character',
            name='deleted_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='character',
            name='is_deleted',
            field=models.BooleanField(default=False),
        ),
        migrations.AddIndex(
            model_name='character',
            index=models.Index(fields=['user'], name='core_charac_user_id_24d1e8_idx'),
        ),
        migrations.AddIndex(
            model_name='character',
            index=models.Index(fields=['public'], name='core_charac_public_97ce2a_idx'),
        ),
        migrations.AddIndex(
            model_name='character',
            index=models.Index(fields=['is_deleted'], name='core_charac_is_dele_a79e75_idx'),
        ),
        migrations.AddField(
            model_name='chatmessage',
            name='character',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='core.character'),
        ),
        migrations.AddField(
            model_name='chatmessage',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='chatmessage',
            index=models.Index(fields=['character', 'user', 'sent_at'], name='core_chatme_charact_54ca04_idx'),
        ),
    ]
