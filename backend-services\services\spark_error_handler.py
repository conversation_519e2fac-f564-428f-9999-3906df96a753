from typing import Dict, Any, Optional

class SparkAPIError(Exception):
    """Base exception for Spark API errors."""
    def __init__(self, message, spark_code=None, spark_message=None):
        super().__init__(message)
        self.spark_code = spark_code
        self.spark_message = spark_message

class SparkContentModerationError(SparkAPIError):
    """Exception for Spark API content moderation failures."""
    pass

# Mapping of Spark API error codes to internal error messages
SPARK_ERROR_MAP = {
    10000: ("Internal server error.", "系统内部错误，请稍后再试。", "SYSTEM_ERROR"),
    10001: ("Invalid parameter.", "请求参数无效，请检查输入。", "INVALID_ARGUMENT"),
    10002: ("Signature authentication failed.", "鉴权失败，请检查您的凭证或签名。", "AUTHENTICATION_FAILED"),
    10003: ("Rate limit exceeded.", "请求频率过高，请稍后再试。", "RESOURCE_EXHAUSTED"),
    10004: ("Resource not found.", "请求资源不存在。", "NOT_FOUND"),
    10005: ("Content moderation failed.", "生成内容不符合规范，已被过滤。", "CONTENT_VIOLATION"),
    # Add more specific Spark API error codes as needed based on their documentation
    # Example: specific image generation errors
}

def handle_spark_api_error(response_header: dict):
    """
    根据星火 API 响应头部处理错误，并抛出相应的自定义异常。

    Args:
        response_header (dict): 星火 API 响应的 'header' 部分。

    Raises:
        SparkAPIError: 如果发生通用 API 错误。
        SparkContentModerationError: 如果内容审核失败。
    """
    code = response_header.get("code")
    message = response_header.get("message", "Unknown error from Spark API")
    sid = response_header.get("sid", "N/A")

    if code == 0:
        return # No error

    internal_message = "星火 API 请求失败。"
    user_friendly_message = "很抱歉，图片生成服务遇到问题，请稍后再试。"
    internal_code = "UNKNOWN_SPARK_ERROR"

    if code in SPARK_ERROR_MAP:
        internal_message, user_friendly_message, internal_code = SPARK_ERROR_MAP[code]
        if internal_code == "CONTENT_VIOLATION":
            raise SparkContentModerationError(
                user_friendly_message,
                spark_code=code,
                spark_message=message
            )
    
    # 对于其他错误或未映射的错误
    raise SparkAPIError(
        f"{internal_message} Spark Code: {code}, Message: {message}, SID: {sid}",
        spark_code=code,
        spark_message=message
    ) 