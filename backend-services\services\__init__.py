"""
服务模块初始化
"""

def initialize_services():
    """
    初始化所有服务模块
    """
    # 导入日志服务
    from .logging_service import configure_logging
    from .structured_logging import configure_structured_logging
    from .log_storage import configure_production_logging
    from .log_sanitizer import configure_sanitized_logging
    from .alert_notifier import configure_alert_system
    
    # 配置基础日志
    configure_logging()
    
    # 配置结构化日志
    configure_structured_logging()
    
    # 配置生产环境日志存储
    configure_production_logging()
    
    # 配置日志敏感信息脱敏
    configure_sanitized_logging()
    
    # 配置告警系统
    configure_alert_system()
    
    # 记录初始化完成
    print("服务模块初始化完成")