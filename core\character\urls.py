"""角色模块URL配置。"""
from django.urls import path
from core.views import (
    ChatMessageView,
    CharacterGenerateView,
    CharacterSaveView,
    CharacterListView,
    CharacterDetailView,
    CommunityCharacterListView,
    CharacterBackgroundListView,
    CharacterBackgroundRetryView,
    AgentIndexView,
    AgentDetailView
)

urlpatterns = [
    # 角色生成API
    path('generate/', CharacterGenerateView.as_view(), name='character-generate'),

    # 角色保存API
    path('save/', CharacterSaveView.as_view(), name='character-save'),

    # 用户角色列表API
    path('user/', CharacterListView.as_view(), name='character-user-list'),

    # 社区公开角色列表API
    path('public_list/', CommunityCharacterListView.as_view(), name='character-public-list'),

    # 角色详情API
    path('<int:character_id>/', CharacterDetailView.as_view(), name='character-detail'),

    # 聊天API路由
    path('<int:character_id>/chat/', ChatMessageView.as_view(), name='character-chat'),

    # 背景图片API路由
    path('<int:character_id>/backgrounds/', CharacterBackgroundListView.as_view(), name='character-backgrounds'),
    path('<int:character_id>/backgrounds/retry/', CharacterBackgroundRetryView.as_view(), name='character-backgrounds-retry'),

    # Lobe风格的代理API路由
    path('agents/', AgentIndexView.as_view(), name='agent-index'),
    path('agents/<str:agent_id>/', AgentDetailView.as_view(), name='agent-detail'),
]