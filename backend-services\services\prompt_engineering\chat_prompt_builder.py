"""
对话提示词构建器

负责将角色设定转换为对话AI模型所需的提示词
"""

from typing import Dict, List, Optional, Any, Tuple

from .models import CharacterBasicInfo, CharacterSettings, ChatPromptResult


class ChatPromptBuilder:
    """对话提示词构建器，生成适用于对话AI模型的系统提示词和上下文"""
    
    def __init__(self, template_loader):
        """
        初始化对话提示词构建器
        
        Args:
            template_loader: 提供提示词模板的加载器
        """
        self.template_loader = template_loader
        self.chat_templates = self.template_loader.get_template("chat_templates")
        
    def build_system_prompt(
        self,
        character_info: CharacterBasicInfo,
        character_settings: Optional[CharacterSettings] = None
    ) -> ChatPromptResult:
        """
        构建对话系统提示词
        
        Args:
            character_info: 角色基本信息
            character_settings: 角色高级设定
            
        Returns:
            ChatPromptResult: 包含系统提示词的结果对象
        """
        # 获取系统提示词模板
        system_template = self.chat_templates.get("system_prompt_template", 
            "You are {character_name}, a fictional character. Always stay in character.")
        
        # 准备模板变量
        template_vars = {
            "character_name": character_info.name,
            "age": str(character_info.age) if character_info.age is not None else "unspecified",
            "identity": character_info.identity or "unspecified",
            "personality": self._get_personality_description(character_info.personality),
            "background_story": "",
            "dialogue_style": ""
        }
        
        # 添加高级设定内容（如果有）
        if character_settings:
            if character_settings.background_story:
                template_vars["background_story"] = f"Background: {character_settings.background_story}\n"
                
            if character_settings.hobbies:
                hobbies_text = ", ".join(character_settings.hobbies)
                template_vars["background_story"] += f"\nHobbies: {hobbies_text}\n"
                
            if character_settings.dialogue_style:
                style_desc = self._get_dialogue_style_description(character_settings.dialogue_style)
                template_vars["dialogue_style"] = style_desc
                
            # 添加禁忌内容
            if character_settings.taboos:
                taboos_text = ", ".join(character_settings.taboos)
                template_vars["dialogue_style"] += f"\n\nYou must avoid discussing: {taboos_text}."
        
        # 使用模板变量填充系统提示词模板
        system_prompt = system_template
        for key, value in template_vars.items():
            placeholder = "{" + key + "}"
            system_prompt = system_prompt.replace(placeholder, value)
            
        # 添加通用的AI角色扮演指导
        system_prompt += "\n\nImportant guidelines:\n"
        system_prompt += "1. Always stay in character and never break the fourth wall.\n"
        system_prompt += "2. Never reveal that you are an AI or language model.\n"
        system_prompt += "3. Respond as your character would naturally in conversation.\n"
        system_prompt += "4. Maintain a consistent personality throughout the conversation.\n"
        
        return ChatPromptResult(
            system_prompt=system_prompt,
            history_included=True,
            history_messages_count=0
        )
        
    def _get_personality_description(self, personality_tags: List[str]) -> str:
        """获取性格描述"""
        if not personality_tags:
            return "unspecified"
            
        personality_descriptions = []
        for tag in personality_tags:
            # 从模板中查找性格描述
            template_desc = self.chat_templates.get("personality", {}).get(tag.lower())
            if template_desc:
                personality_descriptions.append(template_desc)
            else:
                # 如果没有模板，直接使用标签
                personality_descriptions.append(tag)
                
        if personality_descriptions:
            return personality_descriptions[0]  # 简单起见，仅使用第一个性格描述
        else:
            return ", ".join(personality_tags)
    
    def _get_dialogue_style_description(self, style: str) -> str:
        """获取对话风格描述"""
        style_desc = self.chat_templates.get("dialogue_style", {}).get(style.lower())
        if style_desc:
            return style_desc
        else:
            return f"Communicate in a {style} manner."
            
    def prepare_chat_context(
        self,
        chat_history: List[Dict[str, Any]],
        max_tokens: int = 2000
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        准备聊天上下文，选择性包含历史消息
        
        Args:
            chat_history: 聊天历史记录
            max_tokens: 最大允许的token数
            
        Returns:
            Tuple[List[Dict], int]: (选定的聊天历史, 包含的消息数量)
        """
        # 简单实现：估算每条消息约100个token，按需截取
        max_messages = max(1, max_tokens // 100)
        selected_history = chat_history[-max_messages:] if len(chat_history) > max_messages else chat_history
        return selected_history, len(selected_history) 