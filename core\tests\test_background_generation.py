"""
背景图片生成功能测试
"""
import json
from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.contrib.auth.models import User
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status

from core.models import Character, CharacterBackground, SCENE_TYPES
from core.services.background_generation_service import BackgroundGenerationService
from core.services.background_generation_task import BackgroundGenerationTask


class CharacterBackgroundModelTest(TestCase):
    """背景图片数据模型测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.character = Character.objects.create(
            name='测试角色',
            age=20,
            gender='女性',
            identity='高中生',
            personality='活泼',
            user=self.user
        )
    
    def test_create_background(self):
        """测试创建背景图片记录"""
        background = CharacterBackground.objects.create(
            character=self.character,
            scene_type='classroom',
            scene_name='教室',
            generation_prompt='classroom scene',
            generation_status='pending'
        )
        
        self.assertEqual(background.character, self.character)
        self.assertEqual(background.scene_type, 'classroom')
        self.assertEqual(background.get_scene_display_name(), '教室')
        self.assertEqual(background.generation_status, 'pending')
    
    def test_background_status_methods(self):
        """测试背景状态管理方法"""
        background = CharacterBackground.objects.create(
            character=self.character,
            scene_type='library',
            generation_status='pending'
        )
        
        # 测试标记为生成中
        background.mark_as_generating()
        background.refresh_from_db()
        self.assertEqual(background.generation_status, 'generating')
        self.assertIsNone(background.error_message)
        
        # 测试标记为完成
        test_url = 'http://example.com/image.jpg'
        background.mark_as_completed(test_url)
        background.refresh_from_db()
        self.assertEqual(background.generation_status, 'completed')
        self.assertEqual(background.image_url, test_url)
        self.assertIsNone(background.error_message)
        
        # 测试标记为失败
        error_msg = '生成失败'
        background.mark_as_failed(error_msg)
        background.refresh_from_db()
        self.assertEqual(background.generation_status, 'failed')
        self.assertEqual(background.error_message, error_msg)
    
    def test_scene_display_name(self):
        """测试场景显示名称"""
        background = CharacterBackground.objects.create(
            character=self.character,
            scene_type='magic_tower'
        )
        
        expected_name = SCENE_TYPES.get('magic_tower', 'magic_tower')
        self.assertEqual(background.get_scene_display_name(), expected_name)


class BackgroundGenerationServiceTest(TestCase):
    """背景生成服务测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.character = Character.objects.create(
            name='测试角色',
            age=18,
            gender='女性',
            identity='高中生',
            personality='元气',
            user=self.user
        )
        self.service = BackgroundGenerationService()
    
    def test_get_scene_types_for_identity(self):
        """测试根据身份获取场景类型"""
        # 测试已知身份
        scenes = self.service.get_scene_types_for_identity('高中生', count=3)
        self.assertEqual(len(scenes), 3)
        # 验证返回的场景都在高中生的配置中
        valid_scenes = ['classroom', 'library', 'gymnasium', 'campus', 'dormitory']
        for scene in scenes:
            self.assertIn(scene, valid_scenes)

        # 测试未知身份
        scenes = self.service.get_scene_types_for_identity('未知身份', count=2)
        self.assertEqual(len(scenes), 2)
        # 应该返回默认场景
        default_scenes = ['office', 'library', 'peaceful_space', 'elegant_room']
        for scene in scenes:
            self.assertIn(scene, default_scenes)
    
    def test_generate_scene_prompt(self):
        """测试场景提示词生成"""
        prompt = self.service.generate_scene_prompt('classroom', '高中生')
        
        self.assertIsInstance(prompt, str)
        self.assertIn('classroom', prompt.lower())
        self.assertIn('教室', prompt)
        self.assertLessEqual(len(prompt), 1000)  # 不超过API限制
    
    def test_create_background_records(self):
        """测试创建背景记录"""
        backgrounds = self.service.create_background_records(self.character, count=3)
        
        self.assertEqual(len(backgrounds), 3)
        self.assertEqual(CharacterBackground.objects.filter(character=self.character).count(), 3)
        
        for bg in backgrounds:
            self.assertEqual(bg.character, self.character)
            self.assertEqual(bg.generation_status, 'pending')
            self.assertIsNotNone(bg.generation_prompt)
            self.assertIn(bg.scene_type, SCENE_TYPES.keys())


class BackgroundGenerationTaskTest(TestCase):
    """背景生成任务测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.character = Character.objects.create(
            name='测试角色',
            age=20,
            gender='女性',
            identity='偶像',
            personality='活泼',
            user=self.user
        )
        self.task = BackgroundGenerationTask()
    
    @patch('core.services.background_generation_task.SparkImageService')
    def test_generate_backgrounds_sync(self, mock_spark_service):
        """测试同步生成背景图片"""
        # 模拟成功的生成结果
        mock_spark_service.return_value.generate_multiple_backgrounds.return_value = [
            {'status': 'success', 'image_url': 'http://example.com/1.jpg', 'error': None},
            {'status': 'success', 'image_url': 'http://example.com/2.jpg', 'error': None},
            {'status': 'failed', 'image_url': None, 'error': '生成失败'},
        ]

        # 执行生成
        self.task._generate_backgrounds_sync(self.character.id, count=3)

        # 验证结果
        backgrounds = CharacterBackground.objects.filter(character=self.character)
        self.assertEqual(backgrounds.count(), 3)

        completed_count = backgrounds.filter(generation_status='completed').count()
        failed_count = backgrounds.filter(generation_status='failed').count()

        self.assertEqual(completed_count, 2)
        self.assertEqual(failed_count, 1)
    
    def test_get_generation_status(self):
        """测试获取生成状态"""
        # 创建不同状态的背景记录
        CharacterBackground.objects.create(
            character=self.character,
            scene_type='stage',
            generation_status='completed'
        )
        CharacterBackground.objects.create(
            character=self.character,
            scene_type='backstage',
            generation_status='failed'
        )
        CharacterBackground.objects.create(
            character=self.character,
            scene_type='recording_studio',
            generation_status='pending'
        )
        
        status_info = self.task.get_generation_status(self.character.id)
        
        self.assertEqual(status_info['total'], 3)
        self.assertEqual(status_info['status_counts']['completed'], 1)
        self.assertEqual(status_info['status_counts']['failed'], 1)
        self.assertEqual(status_info['status_counts']['pending'], 1)
        self.assertFalse(status_info['is_complete'])
        self.assertAlmostEqual(status_info['success_rate'], 1/3, places=2)


class BackgroundAPITest(APITestCase):
    """背景图片API测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.other_user = User.objects.create_user(
            username='otheruser',
            password='otherpass123'
        )
        self.character = Character.objects.create(
            name='测试角色',
            age=22,
            gender='女性',
            identity='魔法使',
            personality='神秘',
            user=self.user
        )
        
        # 创建一些背景记录
        self.backgrounds = []
        for i, scene_type in enumerate(['magic_tower', 'enchanted_forest', 'alchemy_lab']):
            bg = CharacterBackground.objects.create(
                character=self.character,
                scene_type=scene_type,
                scene_name=SCENE_TYPES[scene_type],
                image_url=f'http://example.com/{scene_type}.jpg',
                generation_status='completed' if i < 2 else 'failed'
            )
            self.backgrounds.append(bg)
    
    def test_get_backgrounds_authenticated(self):
        """测试获取背景图片列表（已认证）"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('character-backgrounds', kwargs={'character_id': self.character.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertEqual(data['code'], 0)
        self.assertEqual(len(data['data']['backgrounds']), 3)
        self.assertEqual(data['data']['character_id'], self.character.id)
        self.assertIn('generation_status', data['data'])
    
    def test_get_backgrounds_unauthenticated(self):
        """测试未认证访问"""
        url = reverse('character-backgrounds', kwargs={'character_id': self.character.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_get_backgrounds_wrong_user(self):
        """测试访问其他用户的角色"""
        self.client.force_authenticate(user=self.other_user)
        
        url = reverse('character-backgrounds', kwargs={'character_id': self.character.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_get_backgrounds_with_filters(self):
        """测试带筛选条件的查询"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('character-backgrounds', kwargs={'character_id': self.character.id})
        
        # 测试状态筛选
        response = self.client.get(url, {'status': 'completed'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(len(data['data']['backgrounds']), 2)
        
        # 测试场景类型筛选
        response = self.client.get(url, {'scene_type': 'magic_tower'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(len(data['data']['backgrounds']), 1)
        self.assertEqual(data['data']['backgrounds'][0]['scene_type'], 'magic_tower')
    
    def test_retry_backgrounds(self):
        """测试重试背景生成"""
        self.client.force_authenticate(user=self.user)
        
        url = reverse('character-backgrounds-retry', kwargs={'character_id': self.character.id})
        response = self.client.post(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        self.assertEqual(data['code'], 0)
        self.assertIn('重试任务已启动', data['message'])


class CharacterSaveWithBackgroundTest(APITestCase):
    """角色保存时背景生成集成测试"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
    
    @patch('core.services.background_generation_task.background_task_processor.generate_backgrounds_async')
    def test_character_save_triggers_background_generation(self, mock_generate_async):
        """测试角色保存时触发背景生成"""
        self.client.force_authenticate(user=self.user)
        
        character_data = {
            'name': '新角色',
            'age': 25,
            'gender': '女性',
            'identity': '虚拟心理咨询师',
            'personality': '温柔',
            'image_url': 'http://example.com/avatar.jpg'
        }
        
        url = reverse('character-save')
        response = self.client.post(url, character_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        data = response.json()
        
        # 验证响应包含背景生成信息
        self.assertIn('background_generation', data)
        self.assertEqual(data['background_generation'], 'started')
        
        # 验证背景生成任务被调用
        mock_generate_async.assert_called_once()
        call_args = mock_generate_async.call_args
        self.assertEqual(call_args[1]['count'], 4)  # 默认生成4张
