"""
角色背景图片生成服务
"""
import random
import logging
from typing import List, Dict, Tuple
from core.models import Character, CharacterBackground, SCENE_TYPES

logger = logging.getLogger(__name__)

# 身份对应的场景概率配置
IDENTITY_SCENE_PROBABILITIES = {
    "高中生": {
        'classroom': 0.30,
        'library': 0.25,
        'gymnasium': 0.20,
        'campus': 0.15,
        'dormitory': 0.10
    },
    "大学生": {
        'library': 0.30,
        'classroom': 0.25,
        'laboratory': 0.20,
        'campus': 0.15,
        'dormitory': 0.10
    },
    "偶像": {
        'stage': 0.35,
        'recording_studio': 0.25,
        'concert_hall': 0.20,
        'backstage': 0.20
    },
    "虚拟歌姬": {
        'virtual_space': 0.30,
        'recording_studio': 0.25,
        'stage': 0.25,
        'tech_lab': 0.20
    },
    "咖啡店店员": {
        'coffee_shop': 0.50,
        'kitchen': 0.30,
        'office': 0.20
    },
    "魔法使": {
        'magic_tower': 0.30,
        'enchanted_forest': 0.25,
        'alchemy_lab': 0.25,
        'magic_library': 0.20
    },
    "女仆": {
        'mansion_interior': 0.40,
        'elegant_room': 0.30,
        'tea_room': 0.30
    },
    "赛博朋克侦探": {
        'cyberpunk_street': 0.35,
        'neon_city': 0.30,
        'tech_lab': 0.20,
        'office': 0.15
    },
    "异世界公主": {
        'throne_room': 0.30,
        'royal_garden': 0.25,
        'castle_hall': 0.25,
        'ballroom': 0.20
    },
    "游戏NPC": {
        'fantasy_tavern': 0.30,
        'game_world': 0.30,
        'dungeon': 0.25,
        'castle_hall': 0.15
    },
    "虚拟心理咨询师": {
        'counseling_room': 0.40,
        'therapy_office': 0.30,
        'peaceful_space': 0.30
    }
}

# 场景描述模板
SCENE_PROMPT_TEMPLATES = {
    'classroom': {
        'chinese': '教室内部，黑板，课桌椅，窗户，阳光，学习氛围',
        'english': 'classroom interior, blackboard, desks and chairs, windows, sunlight, academic atmosphere'
    },
    'library': {
        'chinese': '图书馆，书架，阅读桌，安静氛围，书籍，学习环境',
        'english': 'library interior, bookshelves, reading tables, quiet atmosphere, books, study environment'
    },
    'gymnasium': {
        'chinese': '体育馆，运动器材，篮球场，健身设备，运动氛围',
        'english': 'gymnasium interior, sports equipment, basketball court, fitness equipment, athletic atmosphere'
    },
    'campus': {
        'chinese': '校园，教学楼，绿树，学生，青春活力',
        'english': 'campus scene, academic buildings, green trees, students, youthful energy'
    },
    'dormitory': {
        'chinese': '宿舍，床铺，书桌，生活用品，温馨氛围',
        'english': 'dormitory room, beds, study desk, daily necessities, cozy atmosphere'
    },
    'laboratory': {
        'chinese': '实验室，实验设备，试管，显微镜，科研氛围',
        'english': 'laboratory, experimental equipment, test tubes, microscope, research atmosphere'
    },
    'office': {
        'chinese': '办公室，办公桌，电脑，文件，专业环境',
        'english': 'office interior, desk, computer, documents, professional environment'
    },
    'meeting_room': {
        'chinese': '会议室，会议桌，投影仪，白板，商务氛围',
        'english': 'meeting room, conference table, projector, whiteboard, business atmosphere'
    },
    'hospital_room': {
        'chinese': '医院病房，病床，医疗设备，干净整洁，专业环境',
        'english': 'hospital room, medical bed, medical equipment, clean and tidy, professional environment'
    },
    'clinic': {
        'chinese': '诊室，检查床，医疗器械，温和灯光，治疗环境',
        'english': 'clinic room, examination bed, medical instruments, soft lighting, treatment environment'
    },
    'operating_room': {
        'chinese': '手术室，手术台，无影灯，医疗设备，无菌环境',
        'english': 'operating room, surgical table, shadowless lamp, medical equipment, sterile environment'
    },
    'hospital_corridor': {
        'chinese': '医院走廊，病房门，护士站，医疗氛围',
        'english': 'hospital corridor, room doors, nurses station, medical atmosphere'
    },
    'coffee_shop': {
        'chinese': '咖啡店内部，咖啡机，木质桌椅，温馨氛围，咖啡香气',
        'english': 'coffee shop interior, coffee machine, wooden tables and chairs, cozy atmosphere, coffee aroma'
    },
    'kitchen': {
        'chinese': '厨房，烹饪设备，食材，整洁环境，美食制作',
        'english': 'kitchen interior, cooking equipment, ingredients, clean environment, food preparation'
    },
    'stage': {
        'chinese': '舞台，聚光灯，音响设备，观众席，表演氛围',
        'english': 'performance stage, spotlights, sound equipment, audience seats, performance atmosphere'
    },
    'recording_studio': {
        'chinese': '录音室，录音设备，隔音材料，专业音响，音乐制作',
        'english': 'recording studio, recording equipment, soundproofing materials, professional audio, music production'
    },
    'concert_hall': {
        'chinese': '音乐厅，舞台，观众席，华丽装饰，音乐氛围',
        'english': 'concert hall, stage, audience seating, elegant decoration, musical atmosphere'
    },
    'backstage': {
        'chinese': '后台，化妆台，服装，镜子，准备区域',
        'english': 'backstage area, makeup table, costumes, mirrors, preparation area'
    },
    'magic_tower': {
        'chinese': '魔法塔内部，魔法阵，水晶球，古老书籍，神秘光芒',
        'english': 'magic tower interior, magic circles, crystal balls, ancient books, mystical glow'
    },
    'enchanted_forest': {
        'chinese': '魔法森林，发光植物，神秘生物，魔法光芒，奇幻氛围',
        'english': 'enchanted forest, glowing plants, mystical creatures, magical light, fantasy atmosphere'
    },
    'alchemy_lab': {
        'chinese': '炼金实验室，药剂瓶，炼金设备，魔法材料，神秘实验',
        'english': 'alchemy laboratory, potion bottles, alchemical equipment, magical materials, mystical experiments'
    },
    'magic_library': {
        'chinese': '魔法图书馆，古老书籍，魔法卷轴，水晶照明，知识宝库',
        'english': 'magic library, ancient books, magical scrolls, crystal lighting, treasure of knowledge'
    },
    'cyberpunk_street': {
        'chinese': '赛博朋克街道，霓虹灯，未来建筑，科技感，夜景',
        'english': 'cyberpunk street, neon lights, futuristic buildings, high-tech atmosphere, night scene'
    },
    'neon_city': {
        'chinese': '霓虹都市，摩天大楼，光影效果，未来科技，城市夜景',
        'english': 'neon city, skyscrapers, light and shadow effects, futuristic technology, urban night scene'
    },
    'tech_lab': {
        'chinese': '科技实验室，高科技设备，全息显示，未来感，创新环境',
        'english': 'tech laboratory, high-tech equipment, holographic displays, futuristic feel, innovation environment'
    },
    'virtual_space': {
        'chinese': '虚拟空间，数字元素，光线效果，科技感，虚拟现实',
        'english': 'virtual space, digital elements, light effects, technological feel, virtual reality'
    },
    'throne_room': {
        'chinese': '王座厅，华丽王座，红毯，柱子，庄严氛围',
        'english': 'throne room, magnificent throne, red carpet, pillars, majestic atmosphere'
    },
    'royal_garden': {
        'chinese': '皇家花园，精美花卉，喷泉，雕塑，优雅环境',
        'english': 'royal garden, exquisite flowers, fountain, sculptures, elegant environment'
    },
    'castle_hall': {
        'chinese': '城堡大厅，石柱，壁炉，盔甲，中世纪氛围',
        'english': 'castle hall, stone pillars, fireplace, armor, medieval atmosphere'
    },
    'ballroom': {
        'chinese': '舞厅，水晶吊灯，大理石地板，华丽装饰，优雅氛围',
        'english': 'ballroom, crystal chandelier, marble floor, luxurious decoration, elegant atmosphere'
    },
    'mansion_interior': {
        'chinese': '豪宅内部，精美家具，水晶吊灯，大理石地板，奢华装饰',
        'english': 'mansion interior, exquisite furniture, crystal chandelier, marble floor, luxurious decoration'
    },
    'elegant_room': {
        'chinese': '优雅房间，精致家具，温和灯光，舒适环境，高雅品味',
        'english': 'elegant room, refined furniture, soft lighting, comfortable environment, sophisticated taste'
    },
    'tea_room': {
        'chinese': '茶室，茶具，和式装饰，宁静氛围，传统文化',
        'english': 'tea room, tea set, Japanese-style decoration, peaceful atmosphere, traditional culture'
    },
    'fantasy_tavern': {
        'chinese': '奇幻酒馆，木质桌椅，壁炉，冒险者，中世纪氛围',
        'english': 'fantasy tavern, wooden tables and chairs, fireplace, adventurers, medieval atmosphere'
    },
    'dungeon': {
        'chinese': '地下城，石墙，火把，宝箱，冒险氛围',
        'english': 'dungeon, stone walls, torches, treasure chests, adventure atmosphere'
    },
    'game_world': {
        'chinese': '游戏世界，像素风格，游戏界面，虚拟环境，互动元素',
        'english': 'game world, pixel style, game interface, virtual environment, interactive elements'
    },
    'counseling_room': {
        'chinese': '心理咨询室，舒适沙发，温和灯光，植物，宁静氛围',
        'english': 'counseling room, comfortable sofa, soft lighting, plants, peaceful atmosphere'
    },
    'therapy_office': {
        'chinese': '治疗办公室，专业设备，温馨装饰，治疗环境，关怀氛围',
        'english': 'therapy office, professional equipment, warm decoration, therapeutic environment, caring atmosphere'
    },
    'peaceful_space': {
        'chinese': '宁静空间，自然光线，简约装饰，放松环境，内心平静',
        'english': 'peaceful space, natural lighting, minimalist decoration, relaxing environment, inner peace'
    }
}


class BackgroundGenerationService:
    """背景图片生成服务"""
    
    def __init__(self):
        self.logger = logger
    
    def get_scene_types_for_identity(self, identity: str, count: int = 4) -> List[str]:
        """
        根据角色身份获取场景类型列表
        
        Args:
            identity: 角色身份
            count: 需要生成的场景数量
            
        Returns:
            场景类型列表
        """
        if identity not in IDENTITY_SCENE_PROBABILITIES:
            self.logger.warning(f"未找到身份 {identity} 的场景配置，使用默认场景")
            # 使用默认场景
            default_scenes = ['office', 'library', 'peaceful_space', 'elegant_room']
            return default_scenes[:count]
        
        scene_probs = IDENTITY_SCENE_PROBABILITIES[identity]
        scenes = list(scene_probs.keys())
        weights = list(scene_probs.values())
        
        # 使用加权随机选择，避免重复
        selected_scenes = []
        available_scenes = scenes.copy()
        available_weights = weights.copy()
        
        for _ in range(min(count, len(available_scenes))):
            # 加权随机选择
            selected_scene = random.choices(available_scenes, weights=available_weights)[0]
            selected_scenes.append(selected_scene)
            
            # 移除已选择的场景，避免重复
            index = available_scenes.index(selected_scene)
            available_scenes.pop(index)
            available_weights.pop(index)
        
        return selected_scenes
    
    def generate_scene_prompt(self, scene_type: str, character_identity: str = None) -> str:
        """
        生成场景提示词，优化以避免内容审核问题

        Args:
            scene_type: 场景类型
            character_identity: 角色身份（可选，用于优化提示词）

        Returns:
            生成的提示词
        """
        if scene_type not in SCENE_PROMPT_TEMPLATES:
            self.logger.warning(f"未找到场景类型 {scene_type} 的提示词模板")
            return f"beautiful {scene_type} environment, clean and bright"

        template = SCENE_PROMPT_TEMPLATES[scene_type]

        # 构建安全的基础提示词
        base_prompt = "beautiful environment scene, clean and bright, peaceful atmosphere"
        english_prompt = f"{base_prompt}, {template['english']}"

        # 添加积极正面的质量词汇
        quality_terms = "high quality, beautiful, clean, bright, peaceful, professional"

        # 简化组合，避免复杂描述
        final_prompt = f"{english_prompt}, {quality_terms}"

        # 确保不超过星火API的1000字符限制
        if len(final_prompt) > 800:  # 留更多余量
            # 使用更简洁的版本
            final_prompt = f"beautiful {template['chinese']}, clean and bright environment"

        return final_prompt
    
    def create_background_records(self, character: Character, count: int = 4) -> List[CharacterBackground]:
        """
        为角色创建背景图片记录
        
        Args:
            character: 角色实例
            count: 需要创建的背景图片数量
            
        Returns:
            创建的背景图片记录列表
        """
        # 获取场景类型
        scene_types = self.get_scene_types_for_identity(character.identity, count)
        
        backgrounds = []
        for scene_type in scene_types:
            # 生成提示词
            prompt = self.generate_scene_prompt(scene_type, character.identity)
            
            # 创建背景记录
            background = CharacterBackground.objects.create(
                character=character,
                scene_type=scene_type,
                scene_name=SCENE_TYPES.get(scene_type, scene_type),
                generation_prompt=prompt,
                generation_status='pending'
            )
            backgrounds.append(background)
            
            self.logger.info(f"为角色 {character.name} 创建背景记录: {scene_type}")
        
        return backgrounds
