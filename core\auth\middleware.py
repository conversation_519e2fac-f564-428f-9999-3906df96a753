"""
认证中间件，用于验证API请求中的JWT Token。
"""
import re
import logging
from django.http import JsonResponse
from core.auth.services.token_service import TokenService
from django.contrib.auth.models import User, AnonymousUser
from django.utils.functional import SimpleLazyObject

logger = logging.getLogger(__name__)

class JWTAuthenticationMiddleware:
    """JWT认证中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        # 不需要验证Token的URL路径模式
        self.exempt_urls = [
            r'^/$',                      # 根路径
            r'^/api/auth/register/?$',   # 注册接口
            r'^/api/auth/login/?$',      # 登录接口
            r'^/api/admin/login/?$',     # 管理员登录接口
            r'^/api/characters/public_list.*$',  # 公开角色列表（支持查询参数）
            r'^/api/characters/generate/?$',  # 角色生成API，测试时不需要认证
            r'^/api/personalities.*$',  # 性格列表API
            r'^/api/identities.*$',      # 身份列表API
            r'^/admin/',                 # 管理后台
            r'^/static/',                # 静态资源
            r'^/media/',                 # 媒体资源
            r'^/favicon.ico$',           # 网站图标
        ]
    
    def __call__(self, request):
        # 对于OPTIONS请求（CORS预检），直接放行
        if request.method == 'OPTIONS':
            return self.get_response(request)

        # 检查是否需要验证Token
        path = request.path_info

        # 检查是否是豁免URL
        is_exempt = any(re.match(url, path) for url in self.exempt_urls)

        if is_exempt:
            # 不需要验证，设置匿名用户并传递给下一个中间件
            request.user = AnonymousUser()
            return self.get_response(request)
        
        # 从Authorization头获取Token
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
        if auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]

            # 首先尝试使用自定义Token验证（用于管理员等）
            payload = TokenService.validate_token(token)

            if payload:
                # 自定义Token有效，将用户信息附加到请求
                user_id = payload.get('user_id')
                try:
                    user = User.objects.get(id=user_id)
                    request.user = user
                    # 记录认证成功的日志
                    logger.info(f"用户 {user.username} 认证成功（自定义Token）")

                    # 如果是管理员Token，添加管理员信息
                    if payload.get('is_admin'):
                        request.is_admin = True
                        request.admin_roles = payload.get('roles', [])
                        logger.info(f"管理员 {user.username} 认证成功，角色: {request.admin_roles}")
                    else:
                        request.is_admin = False
                        request.admin_roles = []
                except User.DoesNotExist:
                    # 用户不存在，认证失败
                    logger.warning(f"Token中的用户ID {user_id} 不存在")
                    return JsonResponse(
                        {"error": "认证失败：用户不存在"},
                        status=401
                    )
            else:
                # 自定义Token验证失败，可能是DRF Simple JWT
                # 对于DRF视图，让DRF处理认证
                # 设置匿名用户，让DRF的认证系统处理
                from django.contrib.auth.models import AnonymousUser
                request.user = AnonymousUser()
                request.is_admin = False
                request.admin_roles = []
                logger.info("使用DRF认证系统处理token")
        else:
            # 没有提供Token
            logger.warning(f"请求路径 {path} 未提供Token")
            return JsonResponse(
                {"error": "认证失败：未提供有效的认证信息"},
                status=401
            )
        
        # 请求预处理完成，继续处理请求
        return self.get_response(request)

class AdminAuthMiddleware:
    """管理员认证中间件，用于验证请求是否来自管理员"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        # 需要管理员权限的URL路径模式
        self.admin_urls = [
            r'^/api/admin/.*$',  # 所有管理API接口
        ]
    
    def __call__(self, request):
        # 检查是否需要管理员权限
        path = request.path_info.lstrip('/')
        if any(re.match(url, path) for url in self.admin_urls):
            # 检查是否已通过JWT认证
            if not hasattr(request, 'user') or not request.user.is_authenticated:
                logger.warning("未认证的管理员API访问")
                return JsonResponse(
                    {"error": "访问被拒绝：请先登录"}, 
                    status=401
                )
            
            # 检查是否具有管理员权限
            if not getattr(request, 'is_admin', False):
                logger.warning(f"非管理员用户 {request.user.username} 尝试访问管理API")
                return JsonResponse(
                    {"error": "访问被拒绝：需要管理员权限"}, 
                    status=403
                )
            
            logger.info(f"管理员 {request.user.username} 访问管理API: {path}")
        
        # 请求预处理完成，继续处理请求
        return self.get_response(request)