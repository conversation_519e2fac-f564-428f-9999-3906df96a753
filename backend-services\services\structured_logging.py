"""
结构化日志与上下文集成模块 - 为虚拟角色平台提供结构化日志记录功能
"""
import json
import logging
import uuid
import threading
from datetime import datetime
import traceback
from functools import wraps
from typing import Optional, Dict, Any, Callable
from django.conf import settings

# 存储请求上下文的线程本地存储
_request_context = threading.local()


class StructuredLogFormatter(logging.Formatter):
    """
    结构化日志格式化器，将日志输出为JSON格式
    """
    def __init__(self, *args, **kwargs):
        self.include_stack_info = kwargs.pop('include_stack_info', False)
        super().__init__(*args, **kwargs)
    
    def format(self, record):
        """
        将日志记录格式化为JSON字符串
        """
        # 基础日志字段
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'service': getattr(settings, 'SERVICE_NAME', 'virtual-character-platform'),
        }
        
        # 添加请求上下文（如果有）
        request_context = get_request_context()
        if request_context:
            log_data.update(request_context)
        
        # 添加异常信息（如果有）
        if record.exc_info:
            exc_type, exc_value, exc_traceback = record.exc_info
            if exc_type is not None:
                log_data['error_type'] = exc_type.__name__
            if exc_value is not None:
                log_data['error_message'] = str(exc_value)
            if self.include_stack_info:
                log_data['stack_trace'] = self.formatException(record.exc_info)

        # 添加额外字段（如果有）
        # LogRecord 的额外字段通常直接添加到对象上，而不是通过 extra 属性
        standard_fields = {
            'name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 'filename',
            'module', 'lineno', 'funcName', 'created', 'msecs', 'relativeCreated',
            'thread', 'threadName', 'processName', 'process', 'getMessage',
            'exc_info', 'exc_text', 'stack_info'
        }

        for key, value in record.__dict__.items():
            if key not in standard_fields and not key.startswith('_'):
                log_data[key] = value
        
        # 将结构化数据转换为JSON字符串
        return json.dumps(log_data, ensure_ascii=False)


def configure_structured_logging():
    """
    配置结构化日志记录
    """
    # 获取根logger
    root_logger = logging.getLogger()
    
    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(StructuredLogFormatter())
    root_logger.addHandler(console_handler)
    
    # 在生产环境中添加文件处理器
    if not settings.DEBUG:
        file_handler = logging.FileHandler('logs/app.log')
        file_handler.setFormatter(StructuredLogFormatter(include_stack_info=True))
        root_logger.addHandler(file_handler)
        
        # 为错误日志单独创建一个文件处理器
        error_handler = logging.FileHandler('logs/error.log')
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(StructuredLogFormatter(include_stack_info=True))
        root_logger.addHandler(error_handler)
    
    return root_logger


def set_request_context(request=None, **context: Any) -> None:
    """
    设置当前请求的上下文信息

    Args:
        request: HTTP请求对象（可选）
        **context: 其他上下文信息
    """
    # 初始化请求上下文
    if not hasattr(_request_context, 'data'):
        _request_context.data = {}
    
    # 生成请求ID（如果没有）
    if 'request_id' not in _request_context.data:
        _request_context.data['request_id'] = str(uuid.uuid4())
    
    # 从请求对象提取信息
    if request:
        _request_context.data.update({
            'method': request.method,
            'path': request.path,
            'ip': request.META.get('REMOTE_ADDR'),
        })
        
        # 添加用户信息（如果已认证）
        if hasattr(request, 'user') and request.user.is_authenticated:
            _request_context.data['user_id'] = request.user.id
    
    # 添加其他上下文信息
    _request_context.data.update(context)


def get_request_context() -> Dict[str, Any]:
    """
    获取当前请求的上下文信息

    Returns:
        当前请求的上下文信息字典
    """
    if hasattr(_request_context, 'data'):
        return _request_context.data
    return {}


def clear_request_context() -> None:
    """
    清除当前请求的上下文信息
    """
    if hasattr(_request_context, 'data'):
        delattr(_request_context, 'data')


class RequestContextMiddleware:
    """
    Django中间件，用于自动设置和清除请求上下文
    """
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # 设置请求上下文
        set_request_context(request)
        
        # 处理请求
        response = self.get_response(request)
        
        # 清除请求上下文
        clear_request_context()
        
        return response


def log_function_call(logger: Optional[logging.Logger] = None):
    """
    装饰器，用于记录函数调用的日志

    Args:
        logger: 用于记录日志的logger实例，如果为None则使用函数所在模块的logger

    Returns:
        装饰后的函数
    """
    def decorator(func: Callable) -> Callable:
        # 如果没有提供logger，则使用函数所在模块的logger
        actual_logger = logger
        if actual_logger is None:
            actual_logger = logging.getLogger(func.__module__)

        @wraps(func)
        def wrapper(*args, **kwargs):
            # 确保 logger 不为 None
            if actual_logger is None:
                return func(*args, **kwargs)

            # 记录函数调用
            actual_logger.debug(f"调用函数 {func.__name__}", extra={
                'function': func.__name__,
                'args_count': len(args),
                'kwargs_keys': list(kwargs.keys()),
            })

            try:
                # 执行函数
                result = func(*args, **kwargs)

                # 记录函数返回
                actual_logger.debug(f"函数 {func.__name__} 返回", extra={
                    'function': func.__name__,
                    'result_type': type(result).__name__,
                })

                return result

            except Exception as e:
                # 记录函数异常
                actual_logger.error(
                    f"函数 {func.__name__} 发生异常: {str(e)}",
                    exc_info=True,
                    extra={
                        'function': func.__name__,
                        'exception_type': e.__class__.__name__,
                    }
                )
                raise

        return wrapper

    return decorator