from django.urls import path, include
from . import views
from . import tts_views

urlpatterns = [
    # 认证和授权API路由
    path('auth/', include('core.auth.urls')),

    # 角色性格与身份API路由
    path('personalities/', views.PersonalityListView.as_view(), name='personality-list'),
    path('identities/', views.IdentityListView.as_view(), name='identity-list'),

    # 图片上传API路由
    path('upload/image/', views.ImageUploadView.as_view(), name='image-upload'),

    # TTS语音相关API路由
    path('tts/voices/', tts_views.TTSVoiceListView.as_view(), name='tts-voice-list'),
    path('tts/recommend/', tts_views.TTSVoiceRecommendationView.as_view(), name='tts-voice-recommend'),
    path('tts/preview/', tts_views.TTSPreviewView.as_view(), name='tts-preview'),
    path('characters/<int:character_id>/voice-settings/', tts_views.CharacterVoiceSettingsView.as_view(), name='character-voice-settings'),

    # 其他API路由将在后面实现
    path('characters/', include('core.character.urls')),
    path('admin/', include('core.admin_api.urls')),  # 管理员API路由
]