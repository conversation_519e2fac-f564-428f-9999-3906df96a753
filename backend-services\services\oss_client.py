import os
import uuid
import oss2
import datetime
from typing import Optional
from dotenv import load_dotenv
from urllib.parse import urlparse

# 加载环境变量
load_dotenv()

class OSSClient:
    """
    阿里云 OSS 客户端，用于图片上传、URL获取、文件删除等操作。
    """

    def __init__(self):
        self.access_key_id = os.getenv("OSS_ACCESS_KEY_ID")
        self.access_key_secret = os.getenv("OSS_ACCESS_KEY_SECRET")
        self.endpoint = os.getenv("OSS_ENDPOINT")
        self.bucket_name = os.getenv("OSS_BUCKET_NAME")
        self.cdn_domain = os.getenv("OSS_CDN_DOMAIN")  # 可选的CDN域名配置

        if not all([self.access_key_id, self.access_key_secret, self.endpoint, self.bucket_name]):
            raise ValueError("OSS configuration (AccessKeyId, AccessKeySecret, Endpoint, BucketName) must be set in environment variables.")

        self.auth = oss2.Auth(self.access_key_id, self.access_key_secret)
        self.bucket = oss2.Bucket(self.auth, self.endpoint, self.bucket_name)
    
    def generate_object_path(self, user_id: Optional[int] = None, character_id: Optional[int] = None, file_extension: str = ".jpg") -> str:
        """
        生成用于存储的对象路径，格式：images/{year}/{month}/{day}/{user_id}/{character_id}/{uuid}{ext}
        
        Args:
            user_id: 用户ID，可选
            character_id: 角色ID，可选
            file_extension: 文件扩展名, 例如 '.jpg'
            
        Returns:
            str: 生成的对象路径名
        """
        today = datetime.datetime.now()
        year, month, day = today.year, today.month, today.day
        
        # 构造路径
        path_components = ["images", str(year), f"{month:02d}", f"{day:02d}"]
        
        # 添加用户ID和角色ID（如果提供）
        if user_id:
            path_components.append(str(user_id))
            
        if character_id:
            path_components.append(str(character_id))
            
        # 添加UUID文件名
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        path_components.append(unique_filename)
        
        # 组合路径
        object_path = "/".join(path_components)
        return object_path

    def upload_image(self, image_data: bytes, user_id: Optional[int] = None, character_id: Optional[int] = None, file_extension: str = ".jpg") -> str:
        """
        上传图片数据到 OSS。

        Args:
            image_data (bytes): 图片的二进制数据。
            user_id: 用户ID，可选
            character_id: 角色ID，可选
            file_extension (str): 文件扩展名，例如 ".jpg", ".png"。

        Returns:
            str: 图片在 OSS 上的可访问 URL。
        """
        # 生成存储路径
        object_path = self.generate_object_path(user_id, character_id, file_extension)
        
        try:
            # 上传文件
            self.bucket.put_object(object_path, image_data)
            
            # 获取URL
            url = self.get_public_url(object_path)
            return url
        except oss2.exceptions.OssError as e:
            print(f"OSS 上传失败: {e}")
            raise
    
    def get_public_url(self, object_path: str) -> str:
        """
        获取对象的公开访问URL
        
        Args:
            object_path: 对象在OSS中的路径
            
        Returns:
            str: 可公开访问的URL
        """
        # 如果配置了CDN域名，使用CDN域名构造URL
        if self.cdn_domain:
            return f"https://{self.cdn_domain}/{object_path}"
        
        # 否则使用OSS默认域名
        parsed_endpoint = urlparse(self.endpoint)
        domain = parsed_endpoint.netloc
        if not domain:  # 如果endpoint格式不包含协议
            domain = self.endpoint
        
        return f"https://{self.bucket_name}.{domain}/{object_path}"
    
    def get_signed_url(self, object_path: str, expires: int = 3600) -> str:
        """
        生成带签名的临时访问URL（用于私有访问控制）
        
        Args:
            object_path: 对象在OSS中的路径
            expires: URL有效期（秒），默认1小时
            
        Returns:
            str: 带签名的临时访问URL
        """
        # 生成包含签名的URL，有效期为指定的秒数
        url = self.bucket.sign_url('GET', object_path, expires)
        return url
    
    def delete_file(self, object_path: str) -> bool:
        """
        从OSS删除文件
        
        Args:
            object_path: 对象在OSS中的路径
            
        Returns:
            bool: 删除是否成功
        """
        try:
            self.bucket.delete_object(object_path)
            return True
        except oss2.exceptions.OssError as e:
            print(f"OSS 删除失败: {e}")
            return False
    
    def extract_object_path_from_url(self, url: str) -> str:
        """
        从URL中提取对象路径
        
        Args:
            url: 完整的OSS URL
            
        Returns:
            str: 对象路径
        """
        parsed_url = urlparse(url)
        
        # 如果使用CDN域名
        if self.cdn_domain and self.cdn_domain in url:
            return parsed_url.path.lstrip('/')
            
        # 如果使用OSS默认域名
        path = parsed_url.path
        if path.startswith('/'):
            path = path[1:]  # 移除开头的斜杠
        
        return path 