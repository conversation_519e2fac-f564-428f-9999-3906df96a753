from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from .models import Character
import sys
import os
import logging

logger = logging.getLogger(__name__)

class TTSVoiceListView(APIView):
    """
    获取支持的TTS音色列表
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """
        获取所有支持的音色
        """
        try:
            # 添加backend-services目录到Python路径
            backend_services_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'backend-services')
            if backend_services_path not in sys.path:
                sys.path.append(backend_services_path)
            
            from services.tts_service import tts_service
            
            if not tts_service:
                return Response({
                    "error": "TTS服务不可用"
                }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
            
            # 获取默认服务商的音色列表
            provider = request.query_params.get('provider', tts_service.default_provider)
            voice_config = tts_service.get_supported_voices(provider)
            
            # 格式化音色列表
            voices = []
            for voice_id, voice_info in voice_config.get('voices', {}).items():
                voices.append({
                    'id': voice_id,
                    'name': voice_info.get('name', voice_id),
                    'description': voice_info.get('desc', ''),
                    'age_group': voice_info.get('age', 'adult'),
                    'gender': 'female' if 'female' in voice_id else 'male' if 'male' in voice_id else 'other',
                    'code': voice_info.get('code', voice_id)
                })
            
            return Response({
                "provider": provider,
                "voices": voices,
                "speeds": voice_config.get('speeds', {}),
                "emotions": voice_config.get('emotions', {})
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"获取TTS音色列表失败: {str(e)}")
            return Response({
                "error": "获取音色列表失败"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TTSVoiceRecommendationView(APIView):
    """
    根据角色特征推荐音色
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """
        根据角色特征推荐合适的音色
        """
        try:
            # 添加backend-services目录到Python路径
            backend_services_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'backend-services')
            if backend_services_path not in sys.path:
                sys.path.append(backend_services_path)
            
            from services.tts_service import tts_service
            
            if not tts_service:
                return Response({
                    "error": "TTS服务不可用"
                }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
            
            # 获取请求参数
            gender = request.data.get('gender', 'female')
            age = request.data.get('age', 20)
            personality = request.data.get('personality', '')
            provider = request.data.get('provider', tts_service.default_provider)
            
            # 获取推荐音色
            recommended_voice = tts_service.get_voice_by_character_traits(
                gender=gender,
                age=age,
                personality=personality,
                provider=provider
            )
            
            # 获取音色详细信息
            voice_config = tts_service.get_supported_voices(provider)
            voice_info = voice_config.get('voices', {}).get(recommended_voice, {})
            
            return Response({
                "recommended_voice": {
                    "id": recommended_voice,
                    "name": voice_info.get('name', recommended_voice),
                    "description": voice_info.get('desc', ''),
                    "code": voice_info.get('code', recommended_voice)
                },
                "reason": f"根据{gender}性别、{age}岁年龄和'{personality}'性格特征推荐"
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"音色推荐失败: {str(e)}")
            return Response({
                "error": "音色推荐失败"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CharacterVoiceSettingsView(APIView):
    """
    角色语音设置管理
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request, character_id):
        """
        获取角色的语音设置
        """
        try:
            character = get_object_or_404(Character, id=character_id)
            
            # 权限检查：只能查看自己的角色
            if character.user != request.user:
                return Response({
                    "error": "无权限访问此角色"
                }, status=status.HTTP_403_FORBIDDEN)
            
            # 获取角色的语音设置
            voice_settings = character.settings.get('voice', {}) if character.settings else {}
            
            return Response({
                "character_id": character_id,
                "voice_settings": {
                    "voice_type": voice_settings.get('voice_type', 'female_sweet'),
                    "speed": voice_settings.get('speed', 'normal'),
                    "emotion": voice_settings.get('emotion', 'neutral'),
                    "volume": voice_settings.get('volume', 50),
                    "pitch": voice_settings.get('pitch', 50),
                    "auto_play": voice_settings.get('auto_play', True)
                }
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"获取角色语音设置失败: {str(e)}")
            return Response({
                "error": "获取语音设置失败"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def put(self, request, character_id):
        """
        更新角色的语音设置
        """
        try:
            character = get_object_or_404(Character, id=character_id)
            
            # 权限检查：只能修改自己的角色
            if character.user != request.user:
                return Response({
                    "error": "无权限修改此角色"
                }, status=status.HTTP_403_FORBIDDEN)
            
            # 获取新的语音设置
            new_voice_settings = {
                "voice_type": request.data.get('voice_type', 'female_sweet'),
                "speed": request.data.get('speed', 'normal'),
                "emotion": request.data.get('emotion', 'neutral'),
                "volume": request.data.get('volume', 50),
                "pitch": request.data.get('pitch', 50),
                "auto_play": request.data.get('auto_play', True)
            }
            
            # 更新角色设置
            if not character.settings:
                character.settings = {}
            
            character.settings['voice'] = new_voice_settings
            character.save()
            
            return Response({
                "message": "语音设置更新成功",
                "voice_settings": new_voice_settings
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"更新角色语音设置失败: {str(e)}")
            return Response({
                "error": "更新语音设置失败"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TTSPreviewView(APIView):
    """
    TTS音色预览
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """
        生成音色预览音频
        """
        try:
            # 添加backend-services目录到Python路径
            backend_services_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'backend-services')
            if backend_services_path not in sys.path:
                sys.path.append(backend_services_path)
            
            from services.tts_service import tts_service
            
            if not tts_service:
                return Response({
                    "error": "TTS服务不可用"
                }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
            
            # 获取预览参数
            voice_type = request.data.get('voice_type', 'female_sweet')
            speed = request.data.get('speed', 'normal')
            emotion = request.data.get('emotion', 'neutral')
            volume = request.data.get('volume', 50)
            pitch = request.data.get('pitch', 50)
            text = request.data.get('text', '你好，这是语音预览，请问你觉得这个声音怎么样？')
            
            # 生成预览音频
            audio_url = tts_service.synthesize_speech(
                text=text,
                voice=voice_type,
                speed=speed,
                emotion=emotion,
                volume=volume,
                pitch=pitch
            )
            
            if audio_url:
                return Response({
                    "audio_url": audio_url,
                    "preview_text": text
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    "error": "音频生成失败"
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
        except Exception as e:
            logger.error(f"TTS预览失败: {str(e)}")
            return Response({
                "error": "音频预览失败"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
