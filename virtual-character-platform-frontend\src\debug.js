// 诊断脚本 - 检查前端页面空白问题
console.log('🔍 开始诊断前端页面空白问题...');

// 1. 检查DOM元素
console.log('📋 检查DOM元素:');
console.log('- #app元素:', document.getElementById('app'));
console.log('- body内容:', document.body.innerHTML.length > 0 ? '有内容' : '空白');
console.log('- React根元素:', document.querySelector('#app')?.children.length || 0, '个子元素');

// 2. 检查React是否正常加载
console.log('⚛️ 检查React状态:');
console.log('- React:', typeof React !== 'undefined' ? '已加载' : '未加载');
console.log('- ReactDOM:', typeof ReactDOM !== 'undefined' ? '已加载' : '未加载');

// 3. 检查控制台错误
console.log('❌ 检查控制台错误:');
const originalError = console.error;
let errorCount = 0;
console.error = function(...args) {
  errorCount++;
  console.log(`错误 ${errorCount}:`, ...args);
  originalError.apply(console, args);
};

// 4. 检查网络请求
console.log('🌐 检查网络请求:');
const originalFetch = window.fetch;
window.fetch = function(...args) {
  console.log('网络请求:', args[0]);
  return originalFetch.apply(this, args);
};

// 5. 检查CSS样式
console.log('🎨 检查CSS样式:');
const appElement = document.getElementById('app');
if (appElement) {
  const styles = window.getComputedStyle(appElement);
  console.log('- display:', styles.display);
  console.log('- visibility:', styles.visibility);
  console.log('- opacity:', styles.opacity);
  console.log('- height:', styles.height);
  console.log('- width:', styles.width);
}

// 6. 检查JavaScript错误
console.log('🐛 监听JavaScript错误:');
window.addEventListener('error', (event) => {
  console.log('JavaScript错误:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.log('未处理的Promise拒绝:', event.reason);
});

console.log('✅ 诊断脚本已启动，请查看控制台输出');
