import { AGENT_MARKET_URL } from '../constants/common';
import { isLocaleNotSupport } from '../constants/locale';
import { type Locales, normalizeLocale } from '../locales/resources';
import { characterAPIAdapter } from './characterAPIAdapter';
import { type UnifiedCharacter } from '../types/agent';

/**
 * 请求线上 Agent index（原始实现，保持兼容性）
 */
export const getAgentIndexOriginal = async (locale: Locales) => {
  const url = isLocaleNotSupport(locale)
    ? AGENT_MARKET_URL
    : `${AGENT_MARKET_URL}/index.${normalizeLocale(locale)}.json`;

  const res = await fetch(url);

  return res.json();
};

/**
 * 获取代理索引列表（新实现，使用本地数据）
 */
export const getAgentIndex = async (locale: Locales | string = 'zh-CN'): Promise<{ agents: UnifiedCharacter[] }> => {
  try {
    // 优先尝试使用本地API
    return await characterAPIAdapter.getAgentIndex(locale as string);
  } catch (error) {
    console.warn('本地API失败，尝试使用原始实现:', error);
    // 回退到原始实现
    return await getAgentIndexOriginal(locale as Locales);
  }
};

/**
 * 获取代理详情（原始实现，保持兼容性）
 */
export const getAgentDetailOriginal = async (id: string, locale: Locales) => {
  const url = isLocaleNotSupport(locale)
    ? `${AGENT_MARKET_URL}/${id}.json`
    : `${AGENT_MARKET_URL}/${id}.${normalizeLocale(locale)}.json`;

  const res = await fetch(url);

  return res.json();
};

/**
 * 获取代理详情（新实现，使用本地数据）
 */
export const getAgentDetail = async (id: string, locale: Locales | string = 'zh-CN'): Promise<UnifiedCharacter | null> => {
  try {
    // 优先尝试使用本地API
    return await characterAPIAdapter.getAgentDetail(id, locale as string);
  } catch (error) {
    console.warn('本地API失败，尝试使用原始实现:', error);
    // 回退到原始实现
    return await getAgentDetailOriginal(id, locale as Locales);
  }
};

export const downloadGithubAgent = async (url: string) => {
  const res = await fetch('/api/agent/download', {
    body: JSON.stringify({ url }),
    headers: {
      'Content-Type': 'application/json',
    },
    method: 'POST',
  });

  return res.json();
};
