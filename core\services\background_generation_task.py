"""
背景图片生成任务处理器
"""
import logging
import sys
import os
from threading import Thread
from typing import List
from django.conf import settings

# 添加backend-services目录到Python路径
backend_services_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'backend-services')
if backend_services_path not in sys.path:
    sys.path.append(backend_services_path)

from services.spark_image_service import SparkImageService
from core.models import Character, CharacterBackground
from core.services.background_generation_service import BackgroundGenerationService

logger = logging.getLogger(__name__)


class BackgroundGenerationTask:
    """背景图片生成任务处理器"""
    
    def __init__(self):
        self.spark_service = SparkImageService()
        self.background_service = BackgroundGenerationService()
        self.logger = logger
    
    def generate_backgrounds_async(self, character_id: int, count: int = 4):
        """
        异步生成角色背景图片
        
        Args:
            character_id: 角色ID
            count: 生成数量
        """
        # 在新线程中执行生成任务
        thread = Thread(
            target=self._generate_backgrounds_sync,
            args=(character_id, count),
            daemon=True
        )
        thread.start()
        self.logger.info(f"已启动角色 {character_id} 的背景图片生成任务")
    
    def _generate_backgrounds_sync(self, character_id: int, count: int = 4):
        """
        同步生成角色背景图片（在后台线程中执行）
        
        Args:
            character_id: 角色ID
            count: 生成数量
        """
        try:
            # 获取角色信息
            character = Character.objects.get(id=character_id)
            self.logger.info(f"开始为角色 {character.name} (ID: {character_id}) 生成背景图片")
            
            # 创建背景记录
            backgrounds = self.background_service.create_background_records(character, count)
            
            if not backgrounds:
                self.logger.warning(f"角色 {character_id} 没有创建背景记录")
                return
            
            # 准备提示词列表
            prompts = [bg.generation_prompt for bg in backgrounds]
            
            # 标记所有背景为生成中
            for bg in backgrounds:
                bg.mark_as_generating()
            
            self.logger.info(f"开始批量生成 {len(prompts)} 张背景图片")
            
            # 批量生成图片，传递角色ID和用户ID用于文件存储
            results = self.spark_service.generate_multiple_backgrounds(
                prompts=prompts,
                character_id=character.id,
                user_id=character.user.id
            )
            
            # 处理生成结果
            for i, result in enumerate(results):
                if i < len(backgrounds):
                    background = backgrounds[i]
                    
                    if result['status'] == 'success' and result['image_url']:
                        # 生成成功
                        background.mark_as_completed(result['image_url'])
                        self.logger.info(f"背景图片生成成功: {background.scene_type}")
                    else:
                        # 生成失败
                        error_msg = result.get('error', '未知错误')
                        background.mark_as_failed(error_msg)
                        self.logger.error(f"背景图片生成失败: {background.scene_type} - {error_msg}")
            
            # 统计结果
            success_count = sum(1 for r in results if r['status'] == 'success')
            self.logger.info(f"角色 {character.name} 背景图片生成完成: {success_count}/{len(results)} 成功")
            
        except Character.DoesNotExist:
            self.logger.error(f"角色 {character_id} 不存在")
        except Exception as e:
            self.logger.error(f"角色 {character_id} 背景图片生成异常: {e}")
            
            # 标记所有待生成的背景为失败
            try:
                pending_backgrounds = CharacterBackground.objects.filter(
                    character_id=character_id,
                    generation_status__in=['pending', 'generating']
                )
                for bg in pending_backgrounds:
                    bg.mark_as_failed(str(e))
            except Exception as cleanup_error:
                self.logger.error(f"清理失败状态时出错: {cleanup_error}")
    
    def retry_failed_backgrounds(self, character_id: int):
        """
        重试失败的背景图片生成
        
        Args:
            character_id: 角色ID
        """
        try:
            # 获取失败的背景记录
            failed_backgrounds = CharacterBackground.objects.filter(
                character_id=character_id,
                generation_status='failed'
            )
            
            if not failed_backgrounds.exists():
                self.logger.info(f"角色 {character_id} 没有失败的背景图片需要重试")
                return
            
            self.logger.info(f"开始重试角色 {character_id} 的 {failed_backgrounds.count()} 张失败背景图片")
            
            # 在新线程中执行重试
            thread = Thread(
                target=self._retry_backgrounds_sync,
                args=(list(failed_backgrounds),),
                daemon=True
            )
            thread.start()
            
        except Exception as e:
            self.logger.error(f"重试背景图片生成时出错: {e}")
    
    def _retry_backgrounds_sync(self, backgrounds: List[CharacterBackground]):
        """
        同步重试背景图片生成
        
        Args:
            backgrounds: 需要重试的背景记录列表
        """
        try:
            # 准备提示词列表
            prompts = [bg.generation_prompt for bg in backgrounds]
            
            # 标记为生成中
            for bg in backgrounds:
                bg.mark_as_generating()
            
            # 批量生成图片，获取第一个背景的角色信息用于文件存储
            if backgrounds:
                character = backgrounds[0].character
                results = self.spark_service.generate_multiple_backgrounds(
                    prompts=prompts,
                    character_id=character.id,
                    user_id=character.user.id
                )
            else:
                results = []
            
            # 处理结果
            for i, result in enumerate(results):
                if i < len(backgrounds):
                    background = backgrounds[i]
                    
                    if result['status'] == 'success' and result['image_url']:
                        background.mark_as_completed(result['image_url'])
                        self.logger.info(f"重试成功: {background.scene_type}")
                    else:
                        error_msg = result.get('error', '未知错误')
                        background.mark_as_failed(f"重试失败: {error_msg}")
                        self.logger.error(f"重试失败: {background.scene_type} - {error_msg}")
            
            success_count = sum(1 for r in results if r['status'] == 'success')
            self.logger.info(f"重试完成: {success_count}/{len(results)} 成功")
            
        except Exception as e:
            self.logger.error(f"重试背景图片生成异常: {e}")
            
            # 标记所有为失败
            for bg in backgrounds:
                if bg.generation_status == 'generating':
                    bg.mark_as_failed(f"重试异常: {str(e)}")
    
    def get_generation_status(self, character_id: int) -> dict:
        """
        获取角色背景图片生成状态
        
        Args:
            character_id: 角色ID
            
        Returns:
            状态信息字典
        """
        try:
            backgrounds = CharacterBackground.objects.filter(character_id=character_id)
            
            status_counts = {
                'pending': 0,
                'generating': 0,
                'completed': 0,
                'failed': 0
            }
            
            for bg in backgrounds:
                status_counts[bg.generation_status] += 1
            
            total = backgrounds.count()
            
            return {
                'total': total,
                'status_counts': status_counts,
                'is_complete': status_counts['pending'] == 0 and status_counts['generating'] == 0,
                'success_rate': status_counts['completed'] / total if total > 0 else 0
            }
            
        except Exception as e:
            self.logger.error(f"获取生成状态时出错: {e}")
            return {
                'total': 0,
                'status_counts': {'pending': 0, 'generating': 0, 'completed': 0, 'failed': 0},
                'is_complete': True,
                'success_rate': 0
            }


# 全局任务处理器实例
background_task_processor = BackgroundGenerationTask()
