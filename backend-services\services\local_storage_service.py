"""
本地文件存储服务
用于开发和测试环境，将图片保存到本地media目录
"""
import os
import uuid
import base64
from pathlib import Path
from typing import Optional
from django.conf import settings
from django.urls import reverse
import logging

logger = logging.getLogger(__name__)

class LocalStorageService:
    """本地文件存储服务"""
    
    def __init__(self):
        """初始化本地存储服务"""
        self.media_root = Path(settings.MEDIA_ROOT)
        self.media_url = settings.MEDIA_URL
        
        # 确保媒体目录存在
        self.media_root.mkdir(parents=True, exist_ok=True)
        
        # 创建角色图片目录
        self.characters_dir = self.media_root / 'characters'
        self.characters_dir.mkdir(exist_ok=True)
        
        # 创建背景图片目录
        self.backgrounds_dir = self.media_root / 'backgrounds'
        self.backgrounds_dir.mkdir(exist_ok=True)
    
    def generate_filename(self, user_id: Optional[int] = None, character_id: Optional[int] = None, 
                         file_extension: str = ".jpg", is_background: bool = False) -> str:
        """
        生成文件名
        
        Args:
            user_id: 用户ID
            character_id: 角色ID
            file_extension: 文件扩展名
            is_background: 是否为背景图片
            
        Returns:
            生成的文件名
        """
        # 生成唯一ID
        unique_id = str(uuid.uuid4())
        
        # 构建文件名
        parts = []
        if user_id:
            parts.append(f"u{user_id}")
        if character_id:
            parts.append(f"c{character_id}")
        
        if parts:
            filename = f"{'_'.join(parts)}_{unique_id}{file_extension}"
        else:
            filename = f"{unique_id}{file_extension}"
        
        return filename
    
    def upload_image_data(self, image_data: bytes, user_id: Optional[int] = None,
                         character_id: Optional[int] = None, file_extension: str = ".jpg",
                         is_background: bool = False) -> str:
        """
        上传图片二进制数据到本地存储
        
        Args:
            image_data: 图片二进制数据
            user_id: 用户ID
            character_id: 角色ID
            file_extension: 文件扩展名
            is_background: 是否为背景图片
            
        Returns:
            图片的访问URL
        """
        try:
            # 选择存储目录
            if is_background:
                storage_dir = self.backgrounds_dir
                url_path = 'backgrounds'
            else:
                storage_dir = self.characters_dir
                url_path = 'characters'
            
            # 生成文件名
            filename = self.generate_filename(user_id, character_id, file_extension, is_background)
            
            # 完整文件路径
            file_path = storage_dir / filename
            
            # 保存文件
            with open(file_path, 'wb') as f:
                f.write(image_data)
            
            # 生成访问URL
            image_url = f"{self.media_url}{url_path}/{filename}"
            
            logger.info(f"图片保存成功: {file_path} -> {image_url}")
            return image_url
            
        except Exception as e:
            logger.error(f"本地图片保存失败: {e}")
            raise Exception(f"本地图片保存失败: {e}")
    
    def upload_base64_image(self, base64_data: str, user_id: Optional[int] = None,
                           character_id: Optional[int] = None, is_background: bool = False) -> str:
        """
        上传Base64编码的图片数据
        
        Args:
            base64_data: Base64编码的图片数据
            user_id: 用户ID
            character_id: 角色ID
            is_background: 是否为背景图片
            
        Returns:
            图片的访问URL
        """
        try:
            # 处理可能包含前缀的base64数据
            if ',' in base64_data:
                header, encoded = base64_data.split(',', 1)
                file_extension = self._get_extension_from_base64_header(header)
            else:
                encoded = base64_data
                file_extension = '.jpg'  # 默认扩展名
            
            # 解码Base64数据
            image_data = base64.b64decode(encoded)
            
            # 上传图片
            return self.upload_image_data(
                image_data=image_data,
                user_id=user_id,
                character_id=character_id,
                file_extension=file_extension,
                is_background=is_background
            )
            
        except Exception as e:
            logger.error(f"Base64图片上传失败: {e}")
            raise Exception(f"Base64图片上传失败: {e}")
    
    def _get_extension_from_base64_header(self, header: str) -> str:
        """从Base64头部获取文件扩展名"""
        if 'image/jpeg' in header:
            return '.jpg'
        elif 'image/png' in header:
            return '.png'
        elif 'image/gif' in header:
            return '.gif'
        elif 'image/webp' in header:
            return '.webp'
        else:
            return '.jpg'  # 默认扩展名
    
    def delete_file(self, url_or_path: str) -> bool:
        """
        删除文件
        
        Args:
            url_or_path: 文件URL或文件路径
            
        Returns:
            删除是否成功
        """
        try:
            # 如果是URL，提取文件路径
            if url_or_path.startswith(self.media_url):
                # 从URL中提取相对路径
                relative_path = url_or_path[len(self.media_url):]
                file_path = self.media_root / relative_path
            elif url_or_path.startswith('http'):
                # 外部URL，无法删除
                logger.warning(f"无法删除外部URL: {url_or_path}")
                return False
            else:
                # 假设是相对路径
                file_path = Path(url_or_path)
            
            # 删除文件
            if file_path.exists():
                file_path.unlink()
                logger.info(f"文件删除成功: {file_path}")
                return True
            else:
                logger.warning(f"文件不存在: {file_path}")
                return False
                
        except Exception as e:
            logger.error(f"文件删除失败: {e}")
            return False
    
    def get_file_info(self, url: str) -> dict:
        """
        获取文件信息
        
        Args:
            url: 文件URL
            
        Returns:
            文件信息字典
        """
        try:
            if url.startswith(self.media_url):
                relative_path = url[len(self.media_url):]
                file_path = self.media_root / relative_path
                
                if file_path.exists():
                    stat = file_path.stat()
                    return {
                        'exists': True,
                        'size': stat.st_size,
                        'created': stat.st_ctime,
                        'modified': stat.st_mtime,
                        'path': str(file_path)
                    }
            
            return {'exists': False}
            
        except Exception as e:
            logger.error(f"获取文件信息失败: {e}")
            return {'exists': False, 'error': str(e)}
    
    def list_files(self, directory: str = 'backgrounds', limit: int = 100) -> list:
        """
        列出目录中的文件
        
        Args:
            directory: 目录名称 ('characters' 或 'backgrounds')
            limit: 返回文件数量限制
            
        Returns:
            文件列表
        """
        try:
            if directory == 'backgrounds':
                target_dir = self.backgrounds_dir
                url_prefix = f"{self.media_url}backgrounds/"
            else:
                target_dir = self.characters_dir
                url_prefix = f"{self.media_url}characters/"
            
            files = []
            for file_path in target_dir.iterdir():
                if file_path.is_file() and len(files) < limit:
                    stat = file_path.stat()
                    files.append({
                        'filename': file_path.name,
                        'url': f"{url_prefix}{file_path.name}",
                        'size': stat.st_size,
                        'created': stat.st_ctime,
                        'modified': stat.st_mtime
                    })
            
            # 按修改时间排序（最新的在前）
            files.sort(key=lambda x: x['modified'], reverse=True)
            return files
            
        except Exception as e:
            logger.error(f"列出文件失败: {e}")
            return []
    
    def get_storage_stats(self) -> dict:
        """
        获取存储统计信息
        
        Returns:
            存储统计字典
        """
        try:
            stats = {
                'characters': {'count': 0, 'size': 0},
                'backgrounds': {'count': 0, 'size': 0},
                'total': {'count': 0, 'size': 0}
            }
            
            # 统计角色图片
            if self.characters_dir.exists():
                for file_path in self.characters_dir.iterdir():
                    if file_path.is_file():
                        stats['characters']['count'] += 1
                        stats['characters']['size'] += file_path.stat().st_size
            
            # 统计背景图片
            if self.backgrounds_dir.exists():
                for file_path in self.backgrounds_dir.iterdir():
                    if file_path.is_file():
                        stats['backgrounds']['count'] += 1
                        stats['backgrounds']['size'] += file_path.stat().st_size
            
            # 计算总计
            stats['total']['count'] = stats['characters']['count'] + stats['backgrounds']['count']
            stats['total']['size'] = stats['characters']['size'] + stats['backgrounds']['size']
            
            return stats
            
        except Exception as e:
            logger.error(f"获取存储统计失败: {e}")
            return {'error': str(e)}
