import React, { useState } from 'react';
import { Tabs, Button, Space } from 'antd';
import { MenuOutlined, SaveOutlined, BookOutlined } from '@ant-design/icons';
import { Flexbox } from 'react-layout-kit';
import classNames from 'classnames';

// 导入已经移植的各个标签页组件
import InfoTab from './tabs/InfoTab';
import RoleTab from './tabs/RoleTab';
import VoiceTab from './tabs/VoiceTab';
import ShellTab from './tabs/ShellTab';
import LangModelTab from './tabs/LangModelTab';

// 导入适配器
import { useTranslationAdapter, useResponsiveAdapter } from '../../adapters/roleAdapter';

interface AdaptedRoleEditProps {
  className?: string;
  style?: React.CSSProperties;
  onToggleSidebar?: () => void;
}

const AdaptedRoleEdit: React.FC<AdaptedRoleEditProps> = ({ 
  className, 
  style, 
  onToggleSidebar 
}) => {
  const [activeTab, setActiveTab] = useState('info');
  const { t } = useTranslationAdapter('role');
  const { md = true } = useResponsiveAdapter();

  const tabItems = [
    {
      key: 'info',
      label: t('nav.info'),
      children: <InfoTab />,
    },
    {
      key: 'role',
      label: t('nav.role'),
      children: <RoleTab />,
    },
    {
      key: 'voice',
      label: t('nav.voice'),
      children: <VoiceTab />,
    },
    {
      key: 'shell',
      label: t('nav.shell'),
      children: <ShellTab />,
    },
    {
      key: 'llm',
      label: t('nav.llm'),
      children: <LangModelTab />,
    },
  ];

  const handleSave = () => {
    // 这里可以添加保存逻辑
    console.log('保存角色配置');
  };

  const handleExport = () => {
    // 这里可以添加导出逻辑
    console.log('导出角色配置');
  };

  return (
    <Flexbox 
      flex={1} 
      gap={12} 
      className={classNames('adapted-role-edit-container', className)} 
      style={style}
    >
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        tabBarExtraContent={{
          left: onToggleSidebar && (
            <Button
              type="text"
              icon={<MenuOutlined />}
              onClick={onToggleSidebar}
              title="切换角色列表"
            />
          ),
          right: (
            <Space size="small">
              <Button
                size="small"
                icon={<BookOutlined />}
                onClick={handleExport}
                title="导出配置"
              >
                导出
              </Button>
              <Button
                type="primary"
                size="small"
                icon={<SaveOutlined />}
                onClick={handleSave}
                title="保存角色"
              >
                保存
              </Button>
            </Space>
          ),
        }}
        className="adapted-role-tabs"
      />
    </Flexbox>
  );
};

export default AdaptedRoleEdit;
