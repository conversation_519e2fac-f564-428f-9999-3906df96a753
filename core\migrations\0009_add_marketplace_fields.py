# Generated by Django 5.2.1 on 2025-07-14 20:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0008_add_character_background'),
    ]

    operations = [
        migrations.AddField(
            model_name='character',
            name='category',
            field=models.CharField(default='Anime', max_length=50, verbose_name='角色分类'),
        ),
        migrations.AddField(
            model_name='character',
            name='chat_count',
            field=models.IntegerField(default=0, verbose_name='聊天次数'),
        ),
        migrations.AddField(
            model_name='character',
            name='downloads',
            field=models.IntegerField(default=0, verbose_name='下载次数'),
        ),
        migrations.AddField(
            model_name='character',
            name='greeting',
            field=models.TextField(blank=True, default='', verbose_name='问候语'),
        ),
        migrations.AddField(
            model_name='character',
            name='homepage',
            field=models.URLField(blank=True, null=True, verbose_name='作者主页'),
        ),
        migrations.AddField(
            model_name='character',
            name='is_official',
            field=models.BooleanField(default=False, verbose_name='官方角色'),
        ),
        migrations.AddField(
            model_name='character',
            name='legacy_data',
            field=models.JSONField(blank=True, default=dict, verbose_name='兼容性数据'),
        ),
        migrations.AddField(
            model_name='character',
            name='likes_count',
            field=models.IntegerField(default=0, verbose_name='点赞数'),
        ),
        migrations.AddField(
            model_name='character',
            name='llm_max_tokens',
            field=models.IntegerField(default=2000, verbose_name='LLM最大令牌数'),
        ),
        migrations.AddField(
            model_name='character',
            name='llm_model',
            field=models.CharField(default='gpt-3.5-turbo', max_length=100, verbose_name='LLM模型'),
        ),
        migrations.AddField(
            model_name='character',
            name='llm_provider',
            field=models.CharField(default='openai', max_length=50, verbose_name='LLM提供商'),
        ),
        migrations.AddField(
            model_name='character',
            name='llm_temperature',
            field=models.FloatField(default=0.7, verbose_name='LLM温度'),
        ),
        migrations.AddField(
            model_name='character',
            name='rating',
            field=models.FloatField(default=0.0, verbose_name='评分'),
        ),
        migrations.AddField(
            model_name='character',
            name='readme',
            field=models.TextField(blank=True, default='', verbose_name='详细说明'),
        ),
        migrations.AddField(
            model_name='character',
            name='system_role',
            field=models.TextField(blank=True, default='', verbose_name='系统角色设定'),
        ),
        migrations.AddField(
            model_name='character',
            name='tags',
            field=models.JSONField(blank=True, default=list, verbose_name='标签列表'),
        ),
        migrations.AddField(
            model_name='character',
            name='view_count',
            field=models.IntegerField(default=0, verbose_name='查看次数'),
        ),
    ]
