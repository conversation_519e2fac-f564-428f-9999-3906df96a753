"""
管理员API视图。
"""
from rest_framework import generics, views, status
from rest_framework.response import Response
from rest_framework.request import Request
from django.contrib.auth.models import User
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from typing import Optional

from core.models import (
    AdminRole,
    UserAdminRole,
    Character,
    PromptTemplate,
    AdminOperationLog,
    SystemConfig,
    CharacterPromptTemplate,
    UserProfile,
)
from core.admin_api.serializers import (
    AdminRoleSerializer,
    UserAdminRoleSerializer,
    AdminUserSerializer,
    AdminCharacterSerializer,
    AdminPromptTemplateSerializer,
    AdminCharacterPromptTemplateSerializer,
    AdminOperationLogSerializer,
    AdminSystemConfigSerializer,
    AdminDashboardSerializer,
)
from core.auth.services.admin_service import AdminService

class AdminDashboardView(views.APIView):
    """管理员仪表盘视图"""

    def get(self, request: Request):
        """获取仪表盘数据"""
        try:
            # 统计数据
            total_users = User.objects.count()
            active_users = User.objects.filter(is_active=True).count()
            total_characters = Character.objects.filter(is_deleted=False).count()
            public_characters = Character.objects.filter(is_deleted=False, public=True).count()
            total_templates = PromptTemplate.objects.count()
            active_templates = PromptTemplate.objects.filter(is_active=True).count()
            
            # 最近的操作日志
            recent_logs = AdminOperationLog.objects.order_by('-created_at')[:10]
            
            # 最近注册的用户
            recent_users = User.objects.order_by('-date_joined')[:5]
            
            # 准备序列化数据
            dashboard_data = {
                'total_users': total_users,
                'active_users': active_users,
                'total_characters': total_characters,
                'public_characters': public_characters,
                'total_templates': total_templates,
                'active_templates': active_templates,
                'recent_logs': recent_logs,
                'recent_users': recent_users,
            }
            
            # 记录操作日志
            AdminService.log_operation(
                user=request.user,
                operation_type='other',
                target_type='other',
                details={'action': 'view_dashboard'},
                request=request
            )
            
            # 序列化并返回数据
            serializer = AdminDashboardSerializer(dashboard_data)
            return Response(serializer.data)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class AdminRoleListView(generics.ListCreateAPIView):
    """管理员角色列表视图"""
    
    queryset = AdminRole.objects.all()
    serializer_class = AdminRoleSerializer
    
    def perform_create(self, serializer):
        """创建角色时记录操作日志"""
        role = serializer.save()
        AdminService.log_operation(
            user=self.request.user,
            operation_type='create',
            target_type='role',
            target_id=role.id,
            details={'role_name': role.name},
            request=self.request
        )

class AdminRoleDetailView(generics.RetrieveUpdateDestroyAPIView):
    """管理员角色详情视图"""
    
    queryset = AdminRole.objects.all()
    serializer_class = AdminRoleSerializer
    
    def perform_update(self, serializer):
        """更新角色时记录操作日志"""
        role = serializer.save()
        AdminService.log_operation(
            user=self.request.user,
            operation_type='update',
            target_type='role',
            target_id=role.id,
            details={'role_name': role.name},
            request=self.request
        )
    
    def perform_destroy(self, instance):
        """删除角色时记录操作日志"""
        role_id = instance.id
        role_name = instance.name
        instance.delete()
        AdminService.log_operation(
            user=self.request.user,
            operation_type='delete',
            target_type='role',
            target_id=role_id,
            details={'role_name': role_name},
            request=self.request
        )

class AdminUserListView(generics.ListAPIView):
    """管理员用户列表视图"""

    queryset = User.objects.all()
    serializer_class = AdminUserSerializer
    request: Request  # 类型注解

    def get_queryset(self):
        """根据查询参数过滤用户"""
        queryset = User.objects.all()

        # 按用户名搜索
        username = self.request.query_params.get('username', None)
        if username:
            queryset = queryset.filter(username__icontains=username)

        # 按邮箱搜索
        email = self.request.query_params.get('email', None)
        if email:
            queryset = queryset.filter(email__icontains=email)

        # 按活跃状态过滤
        is_active = self.request.query_params.get('is_active', None)
        if is_active is not None:
            is_active = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active)

        return queryset

class AdminUserDetailView(generics.RetrieveUpdateAPIView):
    """管理员用户详情视图"""
    
    queryset = User.objects.all()
    serializer_class = AdminUserSerializer
    
    def perform_update(self, serializer):
        """更新用户时记录操作日志"""
        user = serializer.save()
        AdminService.log_operation(
            user=self.request.user,
            operation_type='update',
            target_type='user',
            target_id=user.id,
            details={'username': user.username},
            request=self.request
        )

class AdminCharacterListView(generics.ListAPIView):
    """管理员角色列表视图"""

    queryset = Character.objects.filter(is_deleted=False)
    serializer_class = AdminCharacterSerializer
    request: Request  # 类型注解

    def get_queryset(self):
        """根据查询参数过滤角色"""
        queryset = Character.objects.all()

        # 是否包含已删除的角色
        include_deleted = self.request.query_params.get('include_deleted', 'false')
        if include_deleted.lower() != 'true':
            queryset = queryset.filter(is_deleted=False)

        # 按名称搜索
        name = self.request.query_params.get('name', None)
        if name:
            queryset = queryset.filter(name__icontains=name)

        # 按用户ID过滤
        user_id = self.request.query_params.get('user_id', None)
        if user_id:
            queryset = queryset.filter(user_id=user_id)

        # 按公开状态过滤
        public = self.request.query_params.get('public', None)
        if public is not None:
            public = public.lower() == 'true'
            queryset = queryset.filter(public=public)

        return queryset

class AdminCharacterDetailView(generics.RetrieveUpdateDestroyAPIView):
    """管理员角色详情视图"""
    
    queryset = Character.objects.all()
    serializer_class = AdminCharacterSerializer
    
    def perform_update(self, serializer):
        """更新角色时记录操作日志"""
        character = serializer.save()
        AdminService.log_operation(
            user=self.request.user,
            operation_type='update',
            target_type='character',
            target_id=character.id,
            details={'character_name': character.name},
            request=self.request
        )
    
    def perform_destroy(self, instance):
        """删除角色时记录操作日志"""
        # 执行软删除
        instance.soft_delete()
        AdminService.log_operation(
            user=self.request.user,
            operation_type='delete',
            target_type='character',
            target_id=instance.id,
            details={'character_name': instance.name},
            request=self.request
        )

class AdminPromptTemplateListView(generics.ListCreateAPIView):
    """管理员提示词模板列表视图"""

    queryset = PromptTemplate.objects.all()
    serializer_class = AdminPromptTemplateSerializer
    request: Request  # 类型注解

    def get_queryset(self):
        """根据查询参数过滤提示词模板"""
        queryset = PromptTemplate.objects.all()

        # 按名称搜索
        name = self.request.query_params.get('name', None)
        if name:
            queryset = queryset.filter(name__icontains=name)

        # 按类型过滤
        template_type = self.request.query_params.get('type', None)
        if template_type:
            queryset = queryset.filter(type=template_type)

        # 按分类过滤
        category = self.request.query_params.get('category', None)
        if category:
            queryset = queryset.filter(category=category)

        # 按活跃状态过滤
        is_active = self.request.query_params.get('is_active', None)
        if is_active is not None:
            is_active = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active)

        return queryset
    
    def perform_create(self, serializer):
        """创建提示词模板时记录操作日志"""
        # 设置创建者为当前用户
        template = serializer.save(created_by=self.request.user)
        AdminService.log_operation(
            user=self.request.user,
            operation_type='create',
            target_type='prompt_template',
            target_id=template.id,
            details={'template_name': template.name},
            request=self.request
        )

class AdminPromptTemplateDetailView(generics.RetrieveUpdateDestroyAPIView):
    """管理员提示词模板详情视图"""
    
    queryset = PromptTemplate.objects.all()
    serializer_class = AdminPromptTemplateSerializer
    
    def perform_update(self, serializer):
        """更新提示词模板时记录操作日志"""
        template = serializer.save()
        AdminService.log_operation(
            user=self.request.user,
            operation_type='update',
            target_type='prompt_template',
            target_id=template.id,
            details={'template_name': template.name},
            request=self.request
        )
    
    def perform_destroy(self, instance):
        """删除提示词模板时记录操作日志"""
        template_id = instance.id
        template_name = instance.name
        instance.delete()
        AdminService.log_operation(
            user=self.request.user,
            operation_type='delete',
            target_type='prompt_template',
            target_id=template_id,
            details={'template_name': template_name},
            request=self.request
        )

class AdminOperationLogListView(generics.ListAPIView):
    """管理员操作日志列表视图"""

    queryset = AdminOperationLog.objects.all().order_by('-created_at')
    serializer_class = AdminOperationLogSerializer
    request: Request  # 类型注解

    def get_queryset(self):
        """根据查询参数过滤操作日志"""
        queryset = AdminOperationLog.objects.all().order_by('-created_at')

        # 按用户ID过滤
        user_id = self.request.query_params.get('user_id', None)
        if user_id:
            queryset = queryset.filter(user_id=user_id)

        # 按操作类型过滤
        operation_type = self.request.query_params.get('operation_type', None)
        if operation_type:
            queryset = queryset.filter(operation_type=operation_type)

        # 按目标类型过滤
        target_type = self.request.query_params.get('target_type', None)
        if target_type:
            queryset = queryset.filter(target_type=target_type)

        # 按时间范围过滤
        start_date = self.request.query_params.get('start_date', None)
        end_date = self.request.query_params.get('end_date', None)

        if start_date:
            queryset = queryset.filter(created_at__gte=start_date)

        if end_date:
            queryset = queryset.filter(created_at__lte=end_date)

        return queryset

class AdminSystemConfigView(views.APIView):
    """管理员系统配置视图"""

    def get(self, request: Request):
        """获取所有系统配置"""
        configs = SystemConfig.objects.all()
        serializer = AdminSystemConfigSerializer(configs, many=True)
        return Response(serializer.data)

    def post(self, request: Request):
        """创建或更新系统配置"""
        # 使用类型忽略来避免类型检查警告
        key = request.data.get('key')  # type: ignore
        value = request.data.get('value')  # type: ignore
        description = request.data.get('description')  # type: ignore
        
        if not key or not value:
            return Response(
                {'error': '缺少必要参数'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 尝试获取现有配置
        config, created = SystemConfig.objects.update_or_create(
            key=key,
            defaults={
                'value': value,
                'description': description,
                'updated_by': request.user
            }
        )
        
        # 记录操作日志
        AdminService.log_operation(
            user=request.user,
            operation_type='update' if not created else 'create',
            target_type='system_config',
            target_id=key,
            details={'key': key, 'value': value},
            request=request
        )
        
        serializer = AdminSystemConfigSerializer(config)
        return Response(serializer.data) 