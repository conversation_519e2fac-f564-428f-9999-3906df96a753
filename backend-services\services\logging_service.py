"""
日志服务模块 - 为虚拟角色平台提供统一的日志记录功能
"""
import os
import logging
import logging.handlers
from datetime import datetime
import json
from pathlib import Path

# 日志级别映射，用于从配置字符串转换为Python日志级别
LOG_LEVELS = {
    'DEBUG': logging.DEBUG,
    'INFO': logging.INFO,
    'WARNING': logging.WARNING,
    'ERROR': logging.ERROR,
    'CRITICAL': logging.CRITICAL
}

# 日志格式定义
SIMPLE_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
DETAILED_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(pathname)s:%(lineno)d - %(message)s'

# 默认配置，可被环境变量或配置文件覆盖
DEFAULT_CONFIG = {
    'level': 'INFO',
    'format': SIMPLE_FORMAT,
    'console_output': True,
    'file_output': True,
    'log_dir': 'logs',
    'max_bytes': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5,
}


def get_logger(name):
    """
    获取指定名称的logger实例
    
    Args:
        name: logger名称，通常为模块名称
        
    Returns:
        配置好的logger实例
    """
    return logging.getLogger(name)


def configure_logging(config=None):
    """
    配置全局日志系统
    
    Args:
        config: 日志配置字典，如果为None则使用默认配置
    """
    config = config or DEFAULT_CONFIG
    
    # 获取日志级别
    log_level = LOG_LEVELS.get(config.get('level', 'INFO').upper(), logging.INFO)
    
    # 配置根logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 获取日志格式
    log_format = config.get('format', SIMPLE_FORMAT)
    formatter = logging.Formatter(log_format)
    
    # 添加控制台输出
    if config.get('console_output', True):
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # 添加文件输出
    if config.get('file_output', True):
        log_dir = config.get('log_dir', 'logs')
        os.makedirs(log_dir, exist_ok=True)
        
        # 创建按大小轮转的文件处理器
        log_file = os.path.join(log_dir, f'app.log')
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=config.get('max_bytes', 10 * 1024 * 1024),  # 默认10MB
            backupCount=config.get('backup_count', 5)
        )
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # 记录配置完成的信息
    root_logger.info("日志系统初始化完成")
    return root_logger


# 在模块导入时预配置一个默认logger
default_logger = configure_logging() 