from django.apps import AppConfig
import sys
import os


class CoreConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'core'
    
    def ready(self):
        """
        应用就绪时执行的方法
        """
        # 避免在测试或其他命令时运行
        if 'runserver' not in sys.argv and 'uwsgi' not in sys.argv and 'gunicorn' not in sys.argv:
            return
            
        # 导入信号处理器
        import core.signals
        
        # 添加backend-services到Python路径
        backend_services_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'backend-services')
        if backend_services_path not in sys.path:
            sys.path.append(backend_services_path)
        
        # 避免在多进程服务器中重复初始化
        if os.environ.get('RUN_MAIN', None) != 'true':
            return
            
        # 初始化服务模块
        try:
            from services import initialize_services
            initialize_services()
        except ImportError as e:
            print(f"无法导入服务模块初始化函数: {str(e)}")

        # 导入并初始化定时任务管理器
        try:
            from services.scheduled_tasks import ScheduledTaskManager
            
            # 初始化全局任务管理器实例
            import services.scheduled_tasks as tasks_module
            tasks_module.task_manager = ScheduledTaskManager()
            tasks_module.task_manager.start()
            
            print("定时任务管理器已成功启动")
        except ImportError as e:
            print(f"无法导入定时任务管理器: {str(e)}")
