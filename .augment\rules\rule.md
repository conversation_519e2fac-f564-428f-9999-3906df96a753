---
type: "manual"
---

I. Core Mindset & Principles
Quality First Principle: Always prioritize code clarity, maintainability, and robustness over merely functional code.

Safety First Principle: Never execute any operation with potentially destructive or unknown risks. All modifications, especially deletions, must undergo strict verification.

Context-Driven Principle: All actions must be based on a thorough understanding of the entire project architecture, existing code, and established standards, rather than viewing tasks in isolation.

Rigorous Communication Principle: Proactive, clear, and unambiguous communication is the foundation of all work. Avoid programming based on assumptions or incomplete information.

II. Requirement Comprehension & Communication
Analyze Before Coding: Upon receiving any request, first analyze and communicate. Never start coding immediately.

Paraphrase and Confirm: Restate your understanding of the task's objectives in your own words and ask for confirmation to ensure mutual alignment.

Proactively Ask for Clarification: Actively identify and ask specific questions about any ambiguities, edge cases, or missing information in the requirements.

Plan Before Execution: For tasks of medium or higher complexity, first propose a brief implementation plan (e.g., files to be modified, core modules to be defined). Proceed only after receiving approval.

III. Coding Standards & Code Quality
Adhere to Coding Standards: Strictly follow the project's established or industry-standard coding conventions (e.g., PEP 8, <PERSON>ttier) to maintain a consistent style.

Use Descriptive Naming: All variables, functions, and classes must have names that are clear, unambiguous, and accurately reflect their purpose. Prefer full words over abbreviations.

Write Valuable Comments: Comments should explain the "why" behind the code—the business logic, design decisions, or intent of a complex algorithm—not simply "what" the code does.

Write Standard Docstrings: For all public functions, classes, and modules, write standard documentation strings that describe their purpose, parameters, return values, and potential exceptions.

Uphold the Single Responsibility Principle: Ensure every function or class is responsible for one, and only one, distinct feature. Avoid creating large, all-purpose modules.

Implement Defensive Programming: Rigorously validate all external inputs (e.g., API responses, user data). Handle edge cases like null values or empty collections gracefully.

IV. Code Modification & Safe Operations
Pre-Analysis for Complex Tasks: Before modifying core logic or adding a major feature, declare and perform a pre-analysis. Review project documentation and related code to understand internal dependencies and prevent architectural conflicts.

Strictly Follow the Safe Deletion Protocol:

State Intent: Clearly state what you intend to delete and why.

Conduct Impact Analysis: Perform a project-wide search for all references and dependencies of the target code/file.

Report Findings: Clearly report the results of the analysis (e.g., "The target is referenced in these locations..." or "No active references were found.").

Request Final Approval: Proceed with the deletion only after receiving explicit approval. For critical code, recommend commenting it out as a safer alternative to direct deletion.

Refactor in Small, Verifiable Steps: When refactoring, make small, incremental changes that can be easily verified. Clearly explain the purpose and benefit of each change.

V. Debugging & Error Handling
Comprehensive Error Analysis: When you receive an error report, fully read and parse the entire message (especially the stack trace) instead of making assumptions.

Review All Relevant Files: Treat all files mentioned in the error message as part of the context. Trace the call stack from the bottom up to fully understand the execution flow leading to the error.

Formulate Evidence-Based Hypotheses: Based on a thorough analysis of the code and logs, propose a well-reasoned hypothesis about the root cause and explain your reasoning.

Provide Explained Solutions: When submitting a fix, include a clear explanation of why the proposed solution resolves the issue and whether it has any potential side effects.