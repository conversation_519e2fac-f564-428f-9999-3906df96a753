"""
JWT <PERSON>ken生成和验证服务。

提供JWT的生成、签名和验证功能。
"""
import jwt
import logging
import uuid
from datetime import datetime, timedelta, timezone
from django.conf import settings
import os

logger = logging.getLogger(__name__)

# 从环境变量获取JWT密钥，默认生成一个随机密钥
JWT_SECRET_KEY: str = os.environ.get('JWT_SECRET_KEY') or os.environ.get('SECRET_KEY') or uuid.uuid4().hex

# Token过期时间（小时）
JWT_EXPIRATION_HOURS = int(os.environ.get('JWT_EXPIRATION_HOURS', 2))
# 管理员Token过期时间（小时）
ADMIN_JWT_EXPIRATION_HOURS = int(os.environ.get('ADMIN_JWT_EXPIRATION_HOURS', 1))

class TokenService:
    """JWT <PERSON>服务"""
    
    @staticmethod
    def generate_token(user_id, username):
        """
        生成JWT Token。
        
        Args:
            user_id: 用户ID
            username: 用户名
            
        Returns:
            str: 生成的JWT Token
        """
        try:
            now = datetime.now(timezone.utc)
            payload = {
                'user_id': user_id,
                'username': username,
                'iat': now,
                'exp': now + timedelta(hours=JWT_EXPIRATION_HOURS),
                'jti': uuid.uuid4().hex
            }
            
            token = jwt.encode(
                payload,
                JWT_SECRET_KEY,
                algorithm='HS256'
            )
            
            return token
        except Exception as e:
            logger.error(f"生成Token失败: {str(e)}")
            raise ValueError("Token生成失败")
    
    @staticmethod
    def generate_admin_token(user_id, username, roles):
        """
        生成管理员JWT Token。
        
        Args:
            user_id: 用户ID
            username: 用户名
            roles: 管理员角色列表
            
        Returns:
            str: 生成的管理员JWT Token
        """
        try:
            now = datetime.now(timezone.utc)
            payload = {
                'user_id': user_id,
                'username': username,
                'roles': roles,
                'is_admin': True,
                'iat': now,
                'exp': now + timedelta(hours=ADMIN_JWT_EXPIRATION_HOURS),
                'jti': uuid.uuid4().hex
            }
            
            token = jwt.encode(
                payload,
                JWT_SECRET_KEY,
                algorithm='HS256'
            )
            
            return token
        except Exception as e:
            logger.error(f"生成管理员Token失败: {str(e)}")
            raise ValueError("管理员Token生成失败")
    
    @staticmethod
    def validate_token(token):
        """
        验证JWT Token。
        
        Args:
            token: JWT Token
            
        Returns:
            dict: Token的payload部分，如果验证失败返回None
        """
        try:
            payload = jwt.decode(
                token,
                JWT_SECRET_KEY,
                algorithms=['HS256']
            )
            
            # 导入TokenBlacklistService检查Token是否被注销
            from core.auth.services.token_blacklist_service import TokenBlacklistService
            if TokenBlacklistService.is_blacklisted(payload):
                logger.warning(f"Token已被注销: {payload.get('jti', 'unknown')[:8]}...")
                return None
                
            return payload
        except jwt.ExpiredSignatureError:
            logger.warning("Token已过期")
            return None
        except (jwt.InvalidTokenError, jwt.DecodeError) as e:
            logger.warning(f"无效Token: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Token验证异常: {str(e)}")
            return None 