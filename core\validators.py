import re
from typing import Dict, Any, <PERSON>, Tuple, Union, Optional
import logging
import html

logger = logging.getLogger(__name__)

class DataValidator:
    """数据验证和清洗工具类"""
    
    @staticmethod
    def validate_user_data(data: Dict[str, Any]) -> Tu<PERSON>[bool, Dict[str, Any], List[str]]:
        """
        验证和清洗用户数据
        
        Args:
            data: 要验证的用户数据
            
        Returns:
            Tuple[bool, Dict[str, Any], List[str]]: (是否有效, 清洗后的数据, 错误信息列表)
        """
        cleaned_data = {}
        errors = []
        
        # 验证用户名
        if 'username' in data:
            username = data['username']
            # 清洗用户名：去除前后空格
            username = username.strip()
            
            # 验证长度
            if len(username) < 3:
                errors.append("用户名至少需要3个字符")
            elif len(username) > 30:
                errors.append("用户名不能超过30个字符")
            # 验证字符
            elif not re.match(r'^[\w.@+-]+$', username):
                errors.append("用户名只能包含字母、数字和@/./+/-/_字符")
            else:
                cleaned_data['username'] = username
        
        # 验证邮箱
        if 'email' in data:
            email = data['email']
            # 清洗邮箱：去除前后空格
            email = email.strip().lower()
            
            # 验证格式
            if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
                errors.append("邮箱格式不正确")
            else:
                cleaned_data['email'] = email
        
        # 验证密码
        if 'password' in data:
            password = data['password']
            
            # 验证长度
            if len(password) < 8:
                errors.append("密码至少需要8个字符")
            elif len(password) > 128:
                errors.append("密码不能超过128个字符")
            # 验证强度
            elif not re.search(r'[A-Z]', password):
                errors.append("密码需要包含至少一个大写字母")
            elif not re.search(r'[a-z]', password):
                errors.append("密码需要包含至少一个小写字母")
            elif not re.search(r'[0-9]', password):
                errors.append("密码需要包含至少一个数字")
            else:
                cleaned_data['password'] = password
        
        # 验证其他字段
        for key in ['first_name', 'last_name']:
            if key in data:
                value = data[key]
                if isinstance(value, str):
                    # 清洗：去除前后空格
                    value = value.strip()
                    if len(value) <= 30:
                        cleaned_data[key] = value
                    else:
                        errors.append(f"{key}不能超过30个字符")
        
        # 返回验证结果
        is_valid = len(errors) == 0
        return is_valid, cleaned_data, errors
    
    @staticmethod
    def validate_character_data(data: Dict[str, Any]) -> Tuple[bool, Dict[str, Any], List[str]]:
        """
        验证和清洗角色数据
        
        Args:
            data: 要验证的角色数据
            
        Returns:
            Tuple[bool, Dict[str, Any], List[str]]: (是否有效, 清洗后的数据, 错误信息列表)
        """
        cleaned_data = {}
        errors = []
        
        # 验证角色名称
        if 'name' in data:
            name = data['name']
            # 清洗：去除前后空格，HTML转义
            name = html.escape(name.strip())
            
            # 验证长度
            if len(name) < 1:
                errors.append("角色名称不能为空")
            elif len(name) > 50:
                errors.append("角色名称不能超过50个字符")
            else:
                cleaned_data['name'] = name
        
        # 验证年龄
        if 'age' in data:
            age = data['age']
            
            # 类型转换
            try:
                age = int(age)
                # 验证范围
                if age < 0:
                    errors.append("年龄不能为负数")
                elif age > 1000:
                    errors.append("年龄不能超过1000")
                else:
                    cleaned_data['age'] = age
            except (ValueError, TypeError):
                errors.append("年龄必须是整数")
        
        # 验证身份和性格
        for key in ['identity', 'personality']:
            if key in data:
                value = data[key]
                if isinstance(value, str):
                    # 清洗：去除前后空格，HTML转义
                    value = html.escape(value.strip())
                    
                    # 验证长度
                    if len(value) < 1:
                        errors.append(f"{key}不能为空")
                    elif len(value) > 100:
                        errors.append(f"{key}不能超过100个字符")
                    else:
                        cleaned_data[key] = value
                else:
                    errors.append(f"{key}必须是字符串")
        
        # 验证图像URL
        if 'image_url' in data and data['image_url']:
            image_url = data['image_url']

            # 清洗：去除前后空格
            image_url = image_url.strip()

            # 验证URL格式：允许相对路径（以/开头）或完整的HTTP/HTTPS URL
            if image_url.startswith('/') or re.match(r'^https?://\S+$', image_url):
                cleaned_data['image_url'] = image_url
            else:
                errors.append("图像URL格式不正确，请提供有效的URL或相对路径")
        
        # 验证外观参数和设置（JSON字段）
        for key in ['appearance_params', 'settings']:
            if key in data:
                value = data[key]
                
                # 确保是字典类型
                if isinstance(value, dict):
                    # 清洗JSON字段
                    cleaned_json = {}
                    
                    # 遍历字典项，清洗字符串值
                    for k, v in value.items():
                        if isinstance(v, str):
                            # 对字符串进行HTML转义
                            cleaned_json[k] = html.escape(v)
                        else:
                            cleaned_json[k] = v
                    
                    cleaned_data[key] = cleaned_json
                else:
                    errors.append(f"{key}必须是JSON对象")
        
        # 验证公开状态
        if 'public' in data:
            public = data['public']
            
            # 转换为布尔值
            if isinstance(public, bool):
                cleaned_data['public'] = public
            elif isinstance(public, str):
                if public.lower() in ['true', 'yes', '1']:
                    cleaned_data['public'] = True
                elif public.lower() in ['false', 'no', '0']:
                    cleaned_data['public'] = False
                else:
                    errors.append("public字段必须是布尔值")
            else:
                errors.append("public字段必须是布尔值")
        
        # 返回验证结果
        is_valid = len(errors) == 0
        return is_valid, cleaned_data, errors
    
    @staticmethod
    def validate_chat_message_data(data: Dict[str, Any]) -> Tuple[bool, Dict[str, Any], List[str]]:
        """
        验证和清洗聊天消息数据
        
        Args:
            data: 要验证的聊天消息数据
            
        Returns:
            Tuple[bool, Dict[str, Any], List[str]]: (是否有效, 清洗后的数据, 错误信息列表)
        """
        cleaned_data = {}
        errors = []
        
        # 验证消息内容
        if 'content' in data:
            content = data['content']
            
            # 清洗：去除前后空格，HTML转义
            content = html.escape(content.strip())
            
            # 验证长度
            if len(content) < 1:
                errors.append("消息内容不能为空")
            elif len(content) > 10000:
                errors.append("消息内容不能超过10000个字符")
            else:
                cleaned_data['content'] = content
        
        # 验证发送者类型
        if 'sender_type' in data:
            sender_type = data['sender_type']
            
            # 验证类型
            if sender_type not in ['user', 'character']:
                errors.append("发送者类型必须是'user'或'character'")
            else:
                cleaned_data['sender_type'] = sender_type
        
        # 返回验证结果
        is_valid = len(errors) == 0
        return is_valid, cleaned_data, errors
    
    @staticmethod
    def sanitize_string(value: str) -> str:
        """
        对字符串进行基本清洗
        
        Args:
            value: 待清洗的字符串
            
        Returns:
            str: 清洗后的字符串
        """
        if not isinstance(value, str):
            return ""
        
        # 去除前后空格，HTML转义
        return html.escape(value.strip())
    
    @staticmethod
    def sanitize_integer(value: Any, default: int = 0, min_value: Optional[int] = None, max_value: Optional[int] = None) -> int:
        """
        对整数进行基本清洗
        
        Args:
            value: 待清洗的值
            default: 无法转换为整数时的默认值
            min_value: 最小允许值
            max_value: 最大允许值
            
        Returns:
            int: 清洗后的整数
        """
        try:
            # 尝试转换为整数
            result = int(value)
            
            # 检查范围
            if min_value is not None and result < min_value:
                result = min_value
            if max_value is not None and result > max_value:
                result = max_value
                
            return result
        except (ValueError, TypeError):
            return default
    
    @staticmethod
    def sanitize_url(url: str) -> str:
        """
        对URL进行基本清洗
        
        Args:
            url: 待清洗的URL
            
        Returns:
            str: 清洗后的URL，无效则返回空字符串
        """
        if not isinstance(url, str):
            return ""
        
        # 去除前后空格
        url = url.strip()
        
        # 验证URL格式
        if re.match(r'^https?://\S+$', url):
            return url
        else:
            return ""
    
    @staticmethod
    def sanitize_json(data: Any) -> Dict[str, Any]:
        """
        对JSON数据进行基本清洗
        
        Args:
            data: 待清洗的JSON数据
            
        Returns:
            Dict[str, Any]: 清洗后的JSON数据，无效则返回空字典
        """
        if isinstance(data, dict):
            # 清洗字典中的字符串值
            cleaned_data = {}
            for key, value in data.items():
                if isinstance(value, str):
                    cleaned_data[key] = html.escape(value)
                elif isinstance(value, dict):
                    # 递归清洗嵌套字典
                    cleaned_data[key] = DataValidator.sanitize_json(value)
                else:
                    cleaned_data[key] = value
            return cleaned_data
        else:
            return {} 