"""
用户服务，提供用户创建和密码管理功能。
"""
import logging
from django.contrib.auth.models import User
from django.db import transaction
from core.models import UserProfile
from core.auth.services.password_service import PasswordService

logger = logging.getLogger(__name__)

class UserService:
    """用户服务类，处理用户创建和密码更新"""
    
    @staticmethod
    def create_user(username, email, password):
        """
        创建新用户。
        
        Args:
            username: 用户名
            email: 电子邮箱
            password: 密码
            
        Returns:
            User: 创建的用户对象
            
        Raises:
            ValueError: 如果创建用户过程中出错
        """
        try:
            # 检查用户名是否已存在
            if User.objects.filter(username=username).exists():
                # 出于安全考虑，不返回具体是用户名还是邮箱已存在
                raise ValueError("注册信息无效")
            
            # 检查邮箱是否已存在
            if User.objects.filter(email=email).exists():
                # 出于安全考虑，不返回具体是用户名还是邮箱已存在
                raise ValueError("注册信息无效")
            
            # 检查密码强度
            is_valid, error_msg = PasswordService.check_password_strength(password)
            if not is_valid:
                raise ValueError(error_msg)
            
            # 使用事务确保用户和用户资料的创建是原子操作
            with transaction.atomic():
                # 创建用户
                user = User.objects.create_user(
                    username=username,
                    email=email,
                    password=password  # create_user方法会自动哈希密码
                )
                
                # 创建用户资料
                UserProfile.objects.create(user=user)
                
                logger.info(f"成功创建用户: {username}")
                return user
                
        except ValueError as e:
            # 重新抛出ValueError，以便调用者可以处理
            raise
        except Exception as e:
            logger.error(f"创建用户失败: {str(e)}")
            raise ValueError("创建用户时发生错误")
    
    @staticmethod
    def update_password(user, old_password, new_password):
        """
        更新用户密码。
        
        Args:
            user: 用户对象
            old_password: 旧密码
            new_password: 新密码
            
        Returns:
            bool: 是否更新成功
            
        Raises:
            ValueError: 如果更新密码过程中出错
        """
        try:
            # 验证旧密码
            if not user.check_password(old_password):
                # 出于安全考虑，不明确提示是旧密码错误
                raise ValueError("密码更新失败：验证失败")
            
            # 检查新密码强度
            is_valid, error_msg = PasswordService.check_password_strength(new_password)
            if not is_valid:
                raise ValueError(error_msg)
            
            # 更新密码
            user.set_password(new_password)
            user.save()
            
            logger.info(f"成功更新用户 {user.username} 的密码")
            return True
            
        except ValueError as e:
            # 重新抛出ValueError，以便调用者可以处理
            raise
        except Exception as e:
            logger.error(f"更新密码失败: {str(e)}")
            raise ValueError("更新密码时发生错误") 