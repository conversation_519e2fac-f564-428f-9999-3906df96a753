import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

interface UserInfo {
  id: string;
  username: string;
  email: string;
  // ... 其他用户信息字段
}

interface AuthState {
  isLoggedIn: boolean;
  userToken: string | null;
  userInfo: UserInfo | null;
  login: (token: string, user: UserInfo) => void;
  logout: () => void;
}

const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      isLoggedIn: false,
      userToken: null,
      userInfo: null,
      login: (token, user) => set({ isLoggedIn: true, userToken: token, userInfo: user }),
      logout: () => set({ isLoggedIn: false, userToken: null, userInfo: null }),
    }),
    {
      name: 'auth-storage', // name of the item in the storage (must be unique)
      storage: createJSONStorage(() => localStorage), // use localStorage as the storage
    },
  ),
);

export default useAuthStore; 