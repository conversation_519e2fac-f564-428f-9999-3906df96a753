# 角色身份和场景扩展指南

本文档详细说明如何在虚拟角色平台中添加新的角色身份和背景场景类型。

## 目录
1. [添加新的角色身份](#添加新的角色身份)
2. [添加新的场景类型](#添加新的场景类型)
3. [更新提示词模板](#更新提示词模板)
4. [数据库迁移](#数据库迁移)
5. [前端界面更新](#前端界面更新)
6. [测试验证](#测试验证)

## 添加新的角色身份

### 1. 更新身份常量定义

**文件位置**: `core/models.py`

在 `VALID_IDENTITIES` 列表中添加新身份：

```python
VALID_IDENTITIES = [
    "高中生", "大学生", "偶像", "虚拟歌姬", "咖啡店店员", "魔法使", 
    "女仆", "赛博朋克侦探", "异世界公主", "游戏NPC", "虚拟心理咨询师",
    # 添加新身份
    "护士",           # 新增身份示例
    "程序员",         # 新增身份示例
    "厨师"            # 新增身份示例
]
```

### 2. 配置身份对应的场景概率

**文件位置**: `core/services/background_generation_service.py`

在 `IDENTITY_SCENE_PROBABILITIES` 字典中添加新身份的场景配置：

```python
IDENTITY_SCENE_PROBABILITIES = {
    # 现有配置...
    
    # 新增身份配置
    "护士": {
        'hospital_room': 0.35,      # 病房 35%
        'clinic': 0.25,             # 诊室 25%
        'hospital_corridor': 0.20,  # 医院走廊 20%
        'office': 0.20              # 办公室 20%
    },
    "程序员": {
        'office': 0.40,             # 办公室 40%
        'tech_lab': 0.30,           # 科技实验室 30%
        'library': 0.20,            # 图书馆 20%
        'coffee_shop': 0.10         # 咖啡店 10%
    },
    "厨师": {
        'kitchen': 0.50,            # 厨房 50%
        'restaurant': 0.30,         # 餐厅 30%
        'market': 0.20              # 市场 20%
    }
}
```

### 3. 添加身份的图像生成提示词

**文件位置**: `core/prompt_engineering/identity_prompts.py`

#### 3.1 更新英文提示词映射

```python
IDENTITY_TO_ENGLISH_PROMPT = {
    # 现有映射...
    
    # 新增身份提示词
    "护士": "nurse, medical uniform, stethoscope, caring expression, hospital environment, professional appearance",
    "程序员": "programmer, casual clothing, computer setup, coding environment, tech workspace, focused expression",
    "厨师": "chef, chef uniform, chef hat, cooking utensils, kitchen environment, culinary expertise"
}
```

#### 3.2 更新中文提示词映射

```python
IDENTITY_TO_CHINESE_PROMPT = {
    # 现有映射...
    
    # 新增身份提示词
    "护士": "护士，医护制服，听诊器，关怀表情，医院环境，专业形象",
    "程序员": "程序员，休闲服装，电脑设备，编程环境，科技工作空间，专注表情",
    "厨师": "厨师，厨师制服，厨师帽，烹饪用具，厨房环境，烹饪专业"
}
```

#### 3.3 添加对话系统提示词

```python
IDENTITY_TO_DIALOGUE_PROMPT = {
    # 现有映射...
    
    # 新增身份对话提示词
    "护士": "你是一名护士，具有专业的医护知识和温暖的关怀精神。你说话温和耐心，经常关心他人的健康状况。"
           "你可以谈论医护工作、健康知识、病人护理经验，以及如何保持身心健康等话题。",
    "程序员": "你是一名程序员，熟悉各种编程语言和技术。你思维逻辑清晰，喜欢解决技术问题。"
             "你可以谈论编程技术、软件开发、技术趋势、代码优化，以及程序员的日常工作和生活。",
    "厨师": "你是一名厨师，拥有丰富的烹饪经验和对美食的热情。你对食材和烹饪技巧有深入了解。"
           "你可以谈论烹饪技巧、食材搭配、菜谱分享、餐厅经营，以及美食文化等话题。"
}
```

### 4. 更新前端身份选择器

**文件位置**: `virtual-character-platform-frontend/src/components/character/IdentitySelector.tsx`

#### 4.1 添加身份描述

```typescript
const identityDescriptions: Record<string, string> = {
    // 现有描述...
    
    // 新增身份描述
    "护士": "专业医护，温暖关怀，穿着护士制服，背景通常是医院",
    "程序员": "技术专家，逻辑思维，休闲装扮，背景可能是办公室或实验室",
    "厨师": "烹饪大师，美食专家，厨师制服，背景通常是厨房或餐厅"
};
```

#### 4.2 添加身份图标

```typescript
const identityIcons: Record<string, string> = {
    // 现有图标...
    
    // 新增身份图标
    "护士": "👩‍⚕️",
    "程序员": "👨‍💻", 
    "厨师": "👨‍🍳"
};
```

## 添加新的场景类型

### 1. 更新场景类型定义

**文件位置**: `core/models.py`

在 `SCENE_TYPES` 字典中添加新场景：

```python
SCENE_TYPES = {
    # 现有场景...
    
    # 新增场景类型
    'restaurant': '餐厅',
    'market': '市场',
    'server_room': '服务器机房',
    'pharmacy': '药房'
}
```

### 2. 添加场景提示词模板

**文件位置**: `core/services/background_generation_service.py`

在 `SCENE_PROMPT_TEMPLATES` 字典中添加新场景的提示词：

```python
SCENE_PROMPT_TEMPLATES = {
    # 现有模板...
    
    # 新增场景提示词
    'restaurant': {
        'chinese': '餐厅内部，餐桌椅，温馨灯光，美食氛围，服务环境',
        'english': 'restaurant interior, dining tables and chairs, warm lighting, food atmosphere, service environment'
    },
    'market': {
        'chinese': '市场，新鲜食材，摊位，热闹氛围，购物环境',
        'english': 'market scene, fresh ingredients, stalls, bustling atmosphere, shopping environment'
    },
    'server_room': {
        'chinese': '服务器机房，服务器机架，LED指示灯，科技感，数据中心',
        'english': 'server room, server racks, LED indicators, high-tech feel, data center'
    },
    'pharmacy': {
        'chinese': '药房，药品货架，处方台，专业环境，医疗氛围',
        'english': 'pharmacy interior, medicine shelves, prescription counter, professional environment, medical atmosphere'
    }
}
```

## 数据库迁移

### 1. 创建迁移文件

当添加新的身份或场景类型后，需要创建数据库迁移：

```bash
python manage.py makemigrations core --name add_new_identities_and_scenes
```

### 2. 应用迁移

```bash
python manage.py migrate
```

### 3. 验证数据完整性

确保现有数据与新的验证规则兼容。如果有不兼容的数据，需要编写数据迁移脚本。

## 前端界面更新

### 1. 更新身份选择组件

**文件位置**: `virtual-character-platform-frontend/src/components/character/IdentitySelector.tsx`

确保新身份在选择器中正确显示，包括图标、描述和样式。

### 2. 更新类型定义

**文件位置**: `virtual-character-platform-frontend/src/types/character.ts`

如果有TypeScript类型定义，需要更新相应的类型：

```typescript
export type CharacterIdentity = 
    | "高中生" 
    | "大学生" 
    | "偶像" 
    | "虚拟歌姬" 
    | "咖啡店店员" 
    | "魔法使"
    | "女仆" 
    | "赛博朋克侦探" 
    | "异世界公主" 
    | "游戏NPC" 
    | "虚拟心理咨询师"
    // 新增身份类型
    | "护士"
    | "程序员"
    | "厨师";

export type SceneType = 
    | "classroom"
    | "library"
    // ... 现有场景类型
    // 新增场景类型
    | "restaurant"
    | "market"
    | "server_room"
    | "pharmacy";
```

## 测试验证

### 1. 单元测试更新

**文件位置**: `core/tests/test_background_generation.py`

添加新身份和场景的测试用例：

```python
def test_new_identity_scene_generation(self):
    """测试新身份的场景生成"""
    # 测试护士身份
    scenes = self.service.get_scene_types_for_identity('护士', count=3)
    self.assertEqual(len(scenes), 3)
    valid_scenes = ['hospital_room', 'clinic', 'hospital_corridor', 'office']
    for scene in scenes:
        self.assertIn(scene, valid_scenes)

def test_new_scene_prompt_generation(self):
    """测试新场景的提示词生成"""
    prompt = self.service.generate_scene_prompt('restaurant', '厨师')
    self.assertIsInstance(prompt, str)
    self.assertIn('restaurant', prompt.lower())
    self.assertIn('餐厅', prompt)
```

### 2. 集成测试

创建完整的角色并验证背景生成：

```python
def test_new_character_background_generation(self):
    """测试新角色身份的完整背景生成流程"""
    character = Character.objects.create(
        name='测试护士',
        age=28,
        gender='女性',
        identity='护士',
        personality='温柔',
        user=self.user
    )
    
    backgrounds = self.service.create_background_records(character, count=4)
    self.assertEqual(len(backgrounds), 4)
    
    # 验证生成的场景类型都是护士相关的
    nurse_scenes = ['hospital_room', 'clinic', 'hospital_corridor', 'office']
    for bg in backgrounds:
        self.assertIn(bg.scene_type, nurse_scenes)
```

### 3. API测试

使用Postman或类似工具测试新身份的API响应：

```bash
# 创建新身份角色
POST /api/characters/save/
{
    "name": "测试护士",
    "identity": "护士",
    "personality": "温柔",
    "age": 28
}

# 查询背景图片
GET /api/characters/{character_id}/backgrounds/
```

## 注意事项

### 1. 概率配置原则
- 所有概率值之和应该等于1.0
- 选择与身份最相关的场景类型
- 考虑场景的多样性，避免过于单一

### 2. 提示词编写规范
- 英文提示词使用逗号分隔的关键词
- 中文提示词使用顿号分隔
- 避免包含人物描述，专注于场景环境
- 保持提示词长度在合理范围内（总长度<1000字符）

### 3. 向后兼容性
- 添加新身份时不要修改现有身份的配置
- 确保现有角色的背景生成不受影响
- 新场景类型要有对应的提示词模板

### 4. 性能考虑
- 避免添加过多的场景类型导致选择复杂度增加
- 确保新的提示词不会导致API调用失败
- 考虑图片生成的成本和时间

## 完整示例：添加"医生"身份

以下是添加"医生"身份的完整示例：

### 1. 更新models.py
```python
VALID_IDENTITIES = [
    # ... 现有身份
    "医生"  # 新增
]
```

### 2. 更新background_generation_service.py
```python
IDENTITY_SCENE_PROBABILITIES = {
    # ... 现有配置
    "医生": {
        'clinic': 0.30,
        'hospital_room': 0.25,
        'operating_room': 0.20,
        'office': 0.15,
        'hospital_corridor': 0.10
    }
}
```

### 3. 更新identity_prompts.py
```python
IDENTITY_TO_ENGLISH_PROMPT = {
    # ... 现有映射
    "医生": "doctor, medical coat, stethoscope, professional appearance, medical environment, caring expression"
}

IDENTITY_TO_CHINESE_PROMPT = {
    # ... 现有映射  
    "医生": "医生，白大褂，听诊器，专业形象，医疗环境，关怀表情"
}

IDENTITY_TO_DIALOGUE_PROMPT = {
    # ... 现有映射
    "医生": "你是一名医生，拥有专业的医学知识和丰富的临床经验。你说话严谨专业，但也充满人文关怀。你可以谈论医学知识、疾病诊断、治疗方案、健康建议，以及医患关系等话题。"
}
```

### 4. 更新前端组件
```typescript
const identityDescriptions: Record<string, string> = {
    // ... 现有描述
    "医生": "医学专家，救死扶伤，穿着白大褂，背景通常是医院或诊所"
};

const identityIcons: Record<string, string> = {
    // ... 现有图标
    "医生": "👨‍⚕️"
};
```

### 5. 创建并应用迁移
```bash
python manage.py makemigrations core --name add_doctor_identity
python manage.py migrate
```

### 6. 运行测试验证
```bash
python manage.py test core.tests.test_background_generation
```

通过遵循这个指南，您可以轻松地为虚拟角色平台添加新的角色身份和背景场景，确保功能的完整性和一致性。
