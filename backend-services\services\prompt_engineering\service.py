"""
提示词工程服务主接口

该模块提供提示词生成的核心服务接口，用于生成图像和对话提示词
"""

from typing import List, Dict, Optional, Any, Tuple

from .models import (
    CharacterBasicInfo,
    AppearanceParams,
    CharacterSettings,
    ImagePromptResult,
    ChatPromptResult
)
from .template_loader import TemplateLoader
from .image_prompt_builder import ImagePromptBuilder
from .chat_prompt_builder import ChatPromptBuilder
from .nl_description_parser import NLDescriptionParser


class PromptEngineeringService:
    """提示词工程服务类，提供生成各类提示词的功能"""
    
    def __init__(self, template_loader=None):
        """
        初始化提示词工程服务
        
        Args:
            template_loader: 提供提示词模板加载功能的对象，若不提供则使用默认加载器
        """
        self.template_loader = template_loader or TemplateLoader()
        # 加载模板
        self.template_loader.load_templates()
        
        # 初始化各模块
        self.image_prompt_builder = ImagePromptBuilder(self.template_loader)
        self.chat_prompt_builder = ChatPromptBuilder(self.template_loader)
        self.nl_parser = NLDescriptionParser()
        
        # 最大提示词长度 (星火API限制)
        self.MAX_IMAGE_PROMPT_LENGTH = 1000
        
    def generate_image_prompt(
        self,
        character_info: CharacterBasicInfo,
        appearance_params: Optional[AppearanceParams] = None,
        custom_text_description: Optional[str] = None
    ) -> ImagePromptResult:
        """
        生成图像创建提示词
        
        Args:
            character_info: 角色基础信息
            appearance_params: 角色外观参数，用于定制化
            custom_text_description: 用户输入的自然语言描述
            
        Returns:
            ImagePromptResult: 包含正面提示词和负面提示词的结果对象
        """
        # 首先生成基础提示词
        prompt_result = self.image_prompt_builder.build_initial_prompt(
            character_info, appearance_params
        )
        
        # 如果有自然语言描述，解析并添加
        if custom_text_description:
            # 解析自然语言描述
            parsed_features = self.nl_parser.parse_description(custom_text_description)
            
            # 创建临时的外观参数对象
            if appearance_params:
                temp_params = AppearanceParams(
                    hair_style=parsed_features.get("hair_style", appearance_params.hair_style),
                    hair_color=parsed_features.get("hair_color", appearance_params.hair_color),
                    eye_color=parsed_features.get("eye_color", appearance_params.eye_color),
                    skin_tone=parsed_features.get("skin_tone", appearance_params.skin_tone),
                    body_type=parsed_features.get("body_type", appearance_params.body_type),
                    outfit_style=parsed_features.get("outfit_style", appearance_params.outfit_style),
                    facial_expression=parsed_features.get("facial_expression", appearance_params.facial_expression),
                    additional_params=appearance_params.additional_params
                )
            else:
                temp_params = AppearanceParams(
                    hair_style=parsed_features.get("hair_style"),
                    hair_color=parsed_features.get("hair_color"),
                    eye_color=parsed_features.get("eye_color"),
                    skin_tone=parsed_features.get("skin_tone"),
                    body_type=parsed_features.get("body_type"),
                    outfit_style=parsed_features.get("outfit_style"),
                    facial_expression=parsed_features.get("facial_expression")
                )
            
            # 重新生成提示词
            prompt_result = self.image_prompt_builder.build_initial_prompt(
                character_info, temp_params
            )
            
            # 如果有无法解析的复杂描述，作为整体添加
            if not parsed_features and custom_text_description:
                prompt_result = self.image_prompt_builder.update_prompt_with_custom_description(
                    prompt_result.positive_prompt, custom_text_description
                )
        
        return prompt_result
    
    def generate_chat_system_prompt(
        self,
        character_info: CharacterBasicInfo,
        character_settings: Optional[CharacterSettings] = None
    ) -> ChatPromptResult:
        """
        生成对话系统提示词
        
        Args:
            character_info: 角色基础信息
            character_settings: 角色高级设定，用于对话人格塑造
            
        Returns:
            ChatPromptResult: 包含系统提示词的结果对象
        """
        return self.chat_prompt_builder.build_system_prompt(
            character_info, character_settings
        )
    
    def generate_chat_history_context(
        self,
        chat_history: List[Dict[str, Any]],
        max_tokens: int = 2000
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        从聊天历史中选择性包含消息，保证不超过最大token数
        
        Args:
            chat_history: 聊天历史记录列表
            max_tokens: 最大允许的token数
            
        Returns:
            Tuple[List[Dict], int]: 选定的聊天历史和包含的消息数量
        """
        return self.chat_prompt_builder.prepare_chat_context(chat_history, max_tokens)