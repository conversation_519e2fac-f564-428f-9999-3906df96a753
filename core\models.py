from django.db import models
from django.contrib.auth.models import User
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.utils import timezone
from django.core.exceptions import ValidationError
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from django.db.models.manager import Manager

# Create your models here.

# 预定义的性格和身份列表
VALID_PERSONALITIES = [
    "傲娇", "病娇", "元气", "沉稳", "冷酷", "温柔", "活泼", 
    "腼腆", "高冷", "毒舌", "健气系", "哥哥系", "姐姐系"
]

VALID_IDENTITIES = [
    "高中生", "大学生", "偶像", "虚拟歌姬", "咖啡店店员", "魔法使",
    "女仆", "赛博朋克侦探", "异世界公主", "游戏NPC", "虚拟心理咨询师"
]

# 场景类型定义
SCENE_TYPES = {
    # 学生相关场景
    'classroom': '教室',
    'library': '图书馆',
    'gymnasium': '体育馆',
    'campus': '校园',
    'dormitory': '宿舍',
    'laboratory': '实验室',

    # 工作场所场景
    'office': '办公室',
    'meeting_room': '会议室',
    'hospital_room': '病房',
    'clinic': '诊室',
    'operating_room': '手术室',
    'hospital_corridor': '医院走廊',
    'coffee_shop': '咖啡店',
    'kitchen': '厨房',

    # 娱乐表演场景
    'stage': '舞台',
    'recording_studio': '录音室',
    'concert_hall': '音乐厅',
    'backstage': '后台',

    # 奇幻魔法场景
    'magic_tower': '魔法塔',
    'enchanted_forest': '魔法森林',
    'alchemy_lab': '炼金实验室',
    'magic_library': '魔法图书馆',

    # 未来科技场景
    'cyberpunk_street': '赛博朋克街道',
    'neon_city': '霓虹都市',
    'tech_lab': '科技实验室',
    'virtual_space': '虚拟空间',

    # 皇室贵族场景
    'throne_room': '王座厅',
    'royal_garden': '皇家花园',
    'castle_hall': '城堡大厅',
    'ballroom': '舞厅',

    # 服务场所场景
    'mansion_interior': '豪宅内部',
    'elegant_room': '优雅房间',
    'tea_room': '茶室',

    # 游戏场景
    'fantasy_tavern': '奇幻酒馆',
    'dungeon': '地下城',
    'game_world': '游戏世界',

    # 咨询场景
    'counseling_room': '咨询室',
    'therapy_office': '治疗办公室',
    'peaceful_space': '宁静空间'
}

# 有效的场景类型列表
VALID_SCENE_TYPES = list(SCENE_TYPES.keys())

# 生成状态选择
GENERATION_STATUS_CHOICES = [
    ('pending', '待生成'),
    ('generating', '生成中'),
    ('completed', '已完成'),
    ('failed', '生成失败')
]

def validate_personality(value):
    """
    验证性格值是否在预定义列表中
    """
    if value not in VALID_PERSONALITIES:
        raise ValidationError(f"无效的性格类型: {value}")
    return value

def validate_identity(value):
    """
    验证身份值是否在预定义列表中
    """
    if value not in VALID_IDENTITIES:
        raise ValidationError(f"无效的身份类型: {value}")
    return value

def validate_scene_type(value):
    """
    验证场景类型值是否在预定义列表中
    """
    if value not in VALID_SCENE_TYPES:
        raise ValidationError(f"无效的场景类型: {value}")
    return value

# 用户Profile模型 - 扩展Django User模型
class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile', verbose_name='用户')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    is_deleted = models.BooleanField(default=False, verbose_name='是否删除')
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name='删除时间')
    
    def soft_delete(self):
        """软删除用户"""
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.save()
        return True
    
    def restore(self):
        """恢复软删除的用户"""
        self.is_deleted = False
        self.deleted_at = None
        self.save()
        return True
    
    def __str__(self):
        return f"{self.user.username}的资料"

    class Meta:
        verbose_name = '用户资料'
        verbose_name_plural = '用户资料'


# 角色模型
class Character(models.Model):
    if TYPE_CHECKING:
        objects: 'Manager[Character]'
        id: int  # Django自动生成的主键字段
    name = models.CharField(max_length=255, verbose_name='角色名称')
    age = models.IntegerField(verbose_name='年龄')
    gender = models.CharField(max_length=50, default='女性', verbose_name='性别')
    identity = models.CharField(max_length=255, validators=[validate_identity], verbose_name='身份')
    personality = models.CharField(max_length=255, validators=[validate_personality], verbose_name='性格')
    image_url = models.CharField(max_length=500, blank=True, null=True, verbose_name='头像URL')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='characters', verbose_name='创建用户')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    # 商场功能扩展字段
    category = models.CharField(max_length=50, default='Anime', verbose_name='角色分类')
    tags = models.JSONField(default=list, blank=True, verbose_name='标签列表')
    rating = models.FloatField(default=0.0, verbose_name='评分')
    downloads = models.IntegerField(default=0, verbose_name='下载次数')
    is_official = models.BooleanField(default=False, verbose_name='官方角色')

    # Lobe风格扩展字段
    greeting = models.TextField(blank=True, default='', verbose_name='问候语')
    system_role = models.TextField(blank=True, default='', verbose_name='系统角色设定')
    readme = models.TextField(blank=True, default='', verbose_name='详细说明')
    homepage = models.URLField(blank=True, null=True, verbose_name='作者主页')

    # LLM配置
    llm_provider = models.CharField(max_length=50, default='openai', verbose_name='LLM提供商')
    llm_model = models.CharField(max_length=100, default='gpt-3.5-turbo', verbose_name='LLM模型')
    llm_temperature = models.FloatField(default=0.7, verbose_name='LLM温度')
    llm_max_tokens = models.IntegerField(default=2000, verbose_name='LLM最大令牌数')

    # 兼容性和扩展字段
    legacy_data = models.JSONField(default=dict, blank=True, verbose_name='兼容性数据')

    # 统计字段
    likes_count = models.IntegerField(default=0, verbose_name='点赞数')
    chat_count = models.IntegerField(default=0, verbose_name='聊天次数')
    view_count = models.IntegerField(default=0, verbose_name='查看次数')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    public = models.BooleanField(default=False, verbose_name='是否公开')
    appearance_params = JSONField(default=dict, blank=True, null=True, verbose_name='外观参数')
    settings = JSONField(default=dict, blank=True, null=True, verbose_name='系统设置')
    is_deleted = models.BooleanField(default=False, verbose_name='是否删除')
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name='删除时间')
    
    def soft_delete(self):
        """软删除角色"""
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.save()
        return True
    
    def restore(self):
        """恢复软删除的角色"""
        self.is_deleted = False
        self.deleted_at = None
        self.save()
        return True

    class Meta:
        ordering = ['created_at']
        verbose_name = '虚拟角色'
        verbose_name_plural = '虚拟角色'
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['public']),
            models.Index(fields=['is_deleted']),
        ]

    def __str__(self):
        return self.name


# 角色背景图片模型
class CharacterBackground(models.Model):
    """角色背景图片模型"""
    if TYPE_CHECKING:
        objects: 'Manager[CharacterBackground]'
        id: int  # Django自动生成的主键字段

    character = models.ForeignKey(
        Character,
        on_delete=models.CASCADE,
        related_name='backgrounds',
        verbose_name='关联角色'
    )
    scene_type = models.CharField(
        max_length=100,
        validators=[validate_scene_type],
        verbose_name='场景类型'
    )
    scene_name = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name='场景名称'
    )
    image_url = models.CharField(
        max_length=500,
        blank=True,
        null=True,
        verbose_name='背景图片URL'
    )
    generation_prompt = models.TextField(
        blank=True,
        null=True,
        verbose_name='生成提示词'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='生成时间'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )
    generation_status = models.CharField(
        max_length=20,
        choices=GENERATION_STATUS_CHOICES,
        default='pending',
        verbose_name='生成状态'
    )
    error_message = models.TextField(
        blank=True,
        null=True,
        verbose_name='错误信息'
    )

    def get_scene_display_name(self):
        """获取场景类型的显示名称"""
        return SCENE_TYPES.get(self.scene_type, self.scene_type)

    def mark_as_generating(self):
        """标记为生成中"""
        self.generation_status = 'generating'
        self.error_message = None
        self.save(update_fields=['generation_status', 'error_message', 'updated_at'])

    def mark_as_completed(self, image_url):
        """标记为生成完成"""
        self.generation_status = 'completed'
        self.image_url = image_url
        self.error_message = None
        self.save(update_fields=['generation_status', 'image_url', 'error_message', 'updated_at'])

    def mark_as_failed(self, error_message):
        """标记为生成失败"""
        self.generation_status = 'failed'
        self.error_message = error_message
        self.save(update_fields=['generation_status', 'error_message', 'updated_at'])

    class Meta:
        verbose_name = '角色背景图片'
        verbose_name_plural = '角色背景图片'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['character']),
            models.Index(fields=['generation_status']),
            models.Index(fields=['scene_type']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.character.name} - {self.get_scene_display_name()}"


# 聊天消息模型
class ChatMessage(models.Model):
    if TYPE_CHECKING:
        objects: 'Manager[ChatMessage]'
        id: int  # Django自动生成的主键字段

    # 发送者类型枚举
    class SenderType(models.TextChoices):
        USER = 'user', '用户'
        CHARACTER = 'character', '角色'
    
    character = models.ForeignKey(Character, on_delete=models.CASCADE, related_name='messages', verbose_name='角色')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='messages', verbose_name='用户')
    sender_type = models.CharField(max_length=10, choices=SenderType.choices, verbose_name='发送者类型')
    content = models.TextField(verbose_name='消息内容')
    sent_at = models.DateTimeField(auto_now_add=True, verbose_name='发送时间')
    
    class Meta:
        ordering = ['sent_at']
        verbose_name = '聊天消息'
        verbose_name_plural = '聊天消息'
        indexes = [
            models.Index(fields=['character', 'user', 'sent_at']),
        ]

    def __str__(self):
        return f"{self.get_sender_type_display()}消息 - {self.sent_at}"

# 管理员角色表
class AdminRole(models.Model):
    """管理员角色模型"""
    name = models.CharField(max_length=50, unique=True, verbose_name='角色名称')
    description = models.TextField(blank=True, null=True, verbose_name='角色描述')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '管理员角色'
        verbose_name_plural = '管理员角色'

    def __str__(self):
        return self.name

# 用户-管理员角色关联表
class UserAdminRole(models.Model):
    """用户与管理员角色的关联模型"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='admin_roles', verbose_name='用户')
    role = models.ForeignKey(AdminRole, on_delete=models.CASCADE, related_name='users', verbose_name='管理员角色')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        unique_together = ('user', 'role')
        verbose_name = '用户管理员角色'
        verbose_name_plural = '用户管理员角色'
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['role']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.role.name}"

# 提示词模板表
class PromptTemplate(models.Model):
    """提示词模板模型"""
    TYPE_CHOICES = [
        ('image', '图片生成'),
        ('chat', '对话'),
        ('system', '系统'),
    ]
    
    name = models.CharField(max_length=100, verbose_name='模板名称')
    type = models.CharField(max_length=50, choices=TYPE_CHOICES, verbose_name='模板类型')
    category = models.CharField(max_length=50, blank=True, null=True, verbose_name='分类')
    content = models.TextField(verbose_name='模板内容')
    description = models.TextField(blank=True, null=True, verbose_name='描述')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    version = models.CharField(max_length=20, blank=True, null=True, verbose_name='版本')
    variables = JSONField(default=dict, blank=True, null=True, verbose_name='变量配置')
    examples = JSONField(default=dict, blank=True, null=True, verbose_name='示例')
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_prompts', verbose_name='创建者')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '提示词模板'
        verbose_name_plural = '提示词模板'
        indexes = [
            models.Index(fields=['type']),
            models.Index(fields=['category']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"{self.name} ({self.get_type_display()})"

# 角色-提示词模板关联表
class CharacterPromptTemplate(models.Model):
    """角色与提示词模板的关联模型"""
    character = models.ForeignKey(Character, on_delete=models.CASCADE, related_name='prompt_templates', verbose_name='角色')
    template = models.ForeignKey(PromptTemplate, on_delete=models.CASCADE, related_name='characters', verbose_name='提示词模板')
    custom_content = models.TextField(blank=True, null=True, verbose_name='自定义内容')
    variables_values = JSONField(default=dict, blank=True, null=True, verbose_name='变量值')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        unique_together = ('character', 'template')
        verbose_name = '角色提示词模板'
        verbose_name_plural = '角色提示词模板'
        indexes = [
            models.Index(fields=['character']),
            models.Index(fields=['template']),
        ]

    def __str__(self):
        return f"{self.character.name} - {self.template.name}"

# 系统配置表
class SystemConfig(models.Model):
    """系统配置模型"""
    key = models.CharField(max_length=100, primary_key=True, verbose_name='配置键')
    value = models.TextField(verbose_name='配置值')
    description = models.TextField(blank=True, null=True, verbose_name='描述')
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, verbose_name='更新者')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '系统配置'
        verbose_name_plural = '系统配置'

    def __str__(self):
        return self.key

# 管理操作日志表
class AdminOperationLog(models.Model):
    """管理员操作日志模型"""
    OPERATION_TYPES = [
        ('create', '创建'),
        ('update', '更新'),
        ('delete', '删除'),
        ('login', '登录'),
        ('logout', '登出'),
        ('other', '其他'),
    ]
    
    TARGET_TYPES = [
        ('character', '角色'),
        ('prompt_template', '提示词模板'),
        ('user', '用户'),
        ('system_config', '系统配置'),
        ('other', '其他'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='admin_logs', verbose_name='操作用户')
    operation_type = models.CharField(max_length=50, choices=OPERATION_TYPES, verbose_name='操作类型')
    target_type = models.CharField(max_length=50, choices=TARGET_TYPES, blank=True, null=True, verbose_name='目标类型')
    target_id = models.IntegerField(blank=True, null=True, verbose_name='目标ID')
    details = JSONField(default=dict, blank=True, null=True, verbose_name='操作详情')
    ip_address = models.CharField(max_length=50, blank=True, null=True, verbose_name='IP地址')
    user_agent = models.TextField(blank=True, null=True, verbose_name='用户代理')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='操作时间')
    
    class Meta:
        verbose_name = '管理员操作日志'
        verbose_name_plural = '管理员操作日志'
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['operation_type']),
            models.Index(fields=['target_type']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.get_operation_type_display()} - {self.created_at}"
