"""
自然语言描述解析器

负责解析用户输入的自然语言描述，提取关键信息用于提示词生成
"""

import re
from typing import Dict, List, Optional, Tuple, Any


class NLDescriptionParser:
    """自然语言描述解析器，用于解析自然语言定制指令"""
    
    def __init__(self):
        """初始化解析器，设置基本匹配规则"""
        # 定义特征类别
        self.feature_categories = {
            "hair": ["hair", "hairstyle"],
            "hair_color": ["hair color", "hair tone"],
            "eye_color": ["eye color", "eye", "eyes"],
            "skin_tone": ["skin", "skin color", "skin tone"],
            "outfit": ["outfit", "clothes", "clothing", "dress", "wearing"],
            "expression": ["expression", "face", "facial expression", "mood"],
            "body": ["body", "figure", "physique", "build"]
        }
        
        # 定义颜色列表
        self.colors = [
            "red", "blue", "green", "yellow", "purple", "pink", "black", 
            "white", "brown", "orange", "gray", "silver", "golden", "blonde",
            "cyan", "magenta", "violet", "indigo", "teal", "navy",
            "light blue", "dark blue", "light green", "dark green", "crimson"
        ]
        
        # 定义发型列表
        self.hair_styles = [
            "long", "short", "twin tails", "ponytail", "twintails", "pigtails",
            "braided", "curly", "straight", "wavy", "messy", "tidy",
            "bob cut", "pixie cut", "drill", "bun", "side tail"
        ]
        
        # 定义服装列表
        self.outfits = [
            "uniform", "dress", "casual", "formal", "swimsuit", "kimono",
            "yukata", "suit", "t-shirt", "jeans", "skirt", "shorts",
            "hoodie", "sweater", "coat", "jacket", "robe", "armor",
            "school uniform", "maid outfit", "business suit", "gothic",
            "lolita", "sporty", "traditional", "futuristic"
        ]
        
        # 定义表情列表
        self.expressions = [
            "happy", "sad", "angry", "surprised", "scared", "excited",
            "serious", "calm", "nervous", "shy", "confident", "bored",
            "smiling", "crying", "laughing", "frowning", "pouting", 
            "winking", "grinning", "shocked", "worried", "relaxed"
        ]
    
    def parse_description(self, text: str) -> Dict[str, str]:
        """
        解析自然语言描述，提取特征
        
        Args:
            text: 用户输入的自然语言描述
            
        Returns:
            Dict[str, str]: 提取的特征映射
        """
        # 预处理文本
        text = text.lower().strip()
        extracted_features = {}
        
        # 解析发色
        extracted_features.update(self._extract_hair_color(text))
        
        # 解析发型
        extracted_features.update(self._extract_hair_style(text))
        
        # 解析瞳色
        extracted_features.update(self._extract_eye_color(text))
        
        # 解析服装
        extracted_features.update(self._extract_outfit(text))
        
        # 解析表情
        extracted_features.update(self._extract_expression(text))
        
        # 解析肤色
        extracted_features.update(self._extract_skin_tone(text))
        
        return extracted_features
    
    def _extract_hair_color(self, text: str) -> Dict[str, str]:
        """提取发色描述"""
        for color in self.colors:
            patterns = [
                rf"\b{color}\s+hair\b",
                rf"hair\s+(?:color|tone)?\s+(?:is|to|be)?\s+{color}\b",
                rf"change\s+(?:the|her|his)?\s+hair\s+(?:color|tone)?\s+(?:to|into)?\s+{color}\b",
                rf"make\s+(?:the|her|his)?\s+hair\s+{color}\b"
            ]
            
            for pattern in patterns:
                if re.search(pattern, text):
                    return {"hair_color": color}
        return {}
        
    def _extract_hair_style(self, text: str) -> Dict[str, str]:
        """提取发型描述"""
        for style in self.hair_styles:
            patterns = [
                rf"\b{style}\s+hair\b",
                rf"hair\s+style\s+(?:is|to|be)?\s+{style}\b",
                rf"change\s+(?:the|her|his)?\s+hair\s+(?:style|type)?\s+(?:to|into)?\s+{style}\b",
                rf"make\s+(?:the|her|his)?\s+hair\s+{style}\b"
            ]
            
            for pattern in patterns:
                if re.search(pattern, text):
                    return {"hair_style": style}
        return {}
        
    def _extract_eye_color(self, text: str) -> Dict[str, str]:
        """提取瞳色描述"""
        for color in self.colors:
            patterns = [
                rf"\b{color}\s+eyes?\b",
                rf"eyes?\s+(?:color)?\s+(?:is|to|be)?\s+{color}\b",
                rf"change\s+(?:the|her|his)?\s+eyes?\s+(?:color)?\s+(?:to|into)?\s+{color}\b",
                rf"make\s+(?:the|her|his)?\s+eyes?\s+{color}\b"
            ]
            
            for pattern in patterns:
                if re.search(pattern, text):
                    return {"eye_color": color}
        return {}
    
    def _extract_outfit(self, text: str) -> Dict[str, str]:
        """提取服装描述"""
        for outfit in self.outfits:
            patterns = [
                rf"\bwearing\s+(?:a|an)?\s+{outfit}\b",
                rf"\bdressed\s+(?:in|with)?\s+(?:a|an)?\s+{outfit}\b",
                rf"\bin\s+(?:a|an)?\s+{outfit}\b",
                rf"put\s+(?:on|her|him)?\s+(?:in|into)?\s+(?:a|an)?\s+{outfit}\b",
                rf"change\s+(?:the|her|his)?\s+(?:outfit|clothes|clothing)\s+(?:to|into)?\s+(?:a|an)?\s+{outfit}\b"
            ]
            
            for pattern in patterns:
                if re.search(pattern, text):
                    return {"outfit_style": outfit}
        return {}
    
    def _extract_expression(self, text: str) -> Dict[str, str]:
        """提取表情描述"""
        for expr in self.expressions:
            patterns = [
                rf"\b{expr}\s+(?:expression|face|look)\b",
                rf"\blooking\s+{expr}\b",
                rf"\bwith\s+(?:a|an)?\s+{expr}\s+(?:expression|face|look)\b",
                rf"make\s+(?:her|him|the character)?\s+(?:look|appear)?\s+{expr}\b",
                rf"change\s+(?:the|her|his)?\s+expression\s+(?:to|into)?\s+{expr}\b"
            ]
            
            for pattern in patterns:
                if re.search(pattern, text):
                    return {"facial_expression": expr}
        return {}
    
    def _extract_skin_tone(self, text: str) -> Dict[str, str]:
        """提取肤色描述"""
        skin_tones = ["fair", "pale", "tan", "dark", "brown", "black", "olive", "light", "medium"]
        
        for tone in skin_tones:
            patterns = [
                rf"\b{tone}\s+skin\b",
                rf"skin\s+(?:tone|color)?\s+(?:is|to|be)?\s+{tone}\b",
                rf"change\s+(?:the|her|his)?\s+skin\s+(?:tone|color)?\s+(?:to|into)?\s+{tone}\b",
                rf"make\s+(?:the|her|his)?\s+skin\s+{tone}\b"
            ]
            
            for pattern in patterns:
                if re.search(pattern, text):
                    return {"skin_tone": tone}
        return {} 