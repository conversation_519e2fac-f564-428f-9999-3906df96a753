import os
import requests
import json
import base64
import hashlib
import hmac
import time
from datetime import datetime
from typing import Optional, Dict, Any
from dotenv import load_dotenv
import logging

# 加载环境变量
load_dotenv()

logger = logging.getLogger(__name__)

class TTSService:
    """
    TTS语音合成服务，支持多个服务商
    """
    
    def __init__(self):
        # 讯飞TTS配置
        self.xunfei_app_id = os.getenv('XUNFEI_TTS_APP_ID')
        self.xunfei_api_key = os.getenv('XUNFEI_TTS_API_KEY')
        self.xunfei_api_secret = os.getenv('XUNFEI_TTS_API_SECRET')
        
        # 阿里云TTS配置
        self.aliyun_access_key_id = os.getenv('ALIYUN_ACCESS_KEY_ID')
        self.aliyun_access_key_secret = os.getenv('ALIYUN_ACCESS_KEY_SECRET')
        
        # 默认使用讯飞TTS
        self.default_provider = os.getenv('TTS_DEFAULT_PROVIDER', 'xunfei')
        
        # 支持的语音参数 - 多种音色配置
        self.voice_configs = {
            'xunfei': {
                'voices': {
                    # 女声音色
                    'female_sweet': {'code': 'x2_xiaojuan', 'name': '小娟', 'desc': '甜美女声', 'age': 'young'},
                    'female_cute': {'code': 'x2_xiaoyan', 'name': '小燕', 'desc': '可爱女声', 'age': 'young'},
                    'female_gentle': {'code': 'x2_xiaomei', 'name': '小美', 'desc': '温柔女声', 'age': 'adult'},
                    'female_mature': {'code': 'x2_xiaoli', 'name': '小丽', 'desc': '成熟女声', 'age': 'mature'},
                    'female_lively': {'code': 'x2_xiaoxin', 'name': '小欣', 'desc': '活泼女声', 'age': 'young'},
                    'female_elegant': {'code': 'x2_xiaoya', 'name': '小雅', 'desc': '优雅女声', 'age': 'adult'},

                    # 男声音色
                    'male_warm': {'code': 'x2_xiaofeng', 'name': '小峰', 'desc': '温暖男声', 'age': 'adult'},
                    'male_mature': {'code': 'x2_xiaoming', 'name': '小明', 'desc': '成熟男声', 'age': 'mature'},
                    'male_young': {'code': 'x2_xiaohao', 'name': '小浩', 'desc': '青春男声', 'age': 'young'},
                    'male_deep': {'code': 'x2_xiaogang', 'name': '小刚', 'desc': '低沉男声', 'age': 'mature'},
                    'male_gentle': {'code': 'x2_xiaojun', 'name': '小军', 'desc': '温和男声', 'age': 'adult'},
                    'male_energetic': {'code': 'x2_xiaowei', 'name': '小伟', 'desc': '活力男声', 'age': 'young'},

                    # 特殊音色
                    'child_boy': {'code': 'x2_xiaotong', 'name': '小童', 'desc': '男童声', 'age': 'child'},
                    'child_girl': {'code': 'x2_xiaoni', 'name': '小妮', 'desc': '女童声', 'age': 'child'},
                    'elderly_male': {'code': 'x2_laozhang', 'name': '老张', 'desc': '老年男声', 'age': 'elderly'},
                    'elderly_female': {'code': 'x2_laoli', 'name': '老李', 'desc': '老年女声', 'age': 'elderly'}
                },
                'speeds': {
                    'very_slow': 20,
                    'slow': 30,
                    'normal': 50,
                    'fast': 70,
                    'very_fast': 80
                },
                'emotions': {
                    'neutral': 'neutral',
                    'happy': 'happy',
                    'sad': 'sad',
                    'angry': 'angry',
                    'surprised': 'surprised'
                }
            },
            'aliyun': {
                'voices': {
                    # 阿里云音色配置
                    'female_sweet': {'code': 'Xiaoyun', 'name': '小云', 'desc': '甜美女声'},
                    'male_warm': {'code': 'Xiaogang', 'name': '小刚', 'desc': '温暖男声'},
                    'female_cute': {'code': 'Ruoxi', 'name': '若汐', 'desc': '可爱女声'},
                    'male_mature': {'code': 'Siqi', 'name': '思琪', 'desc': '成熟男声'}
                }
            }
        }
    
    def synthesize_speech(
        self,
        text: str,
        voice: str = 'female_sweet',
        speed: str = 'normal',
        emotion: str = 'neutral',
        volume: int = 50,
        pitch: int = 50,
        provider: Optional[str] = None
    ) -> Optional[str]:
        """
        语音合成主方法

        Args:
            text: 要合成的文本
            voice: 语音类型 (female_sweet, male_warm, etc.)
            speed: 语速 (very_slow, slow, normal, fast, very_fast)
            emotion: 情感 (neutral, happy, sad, angry, surprised)
            volume: 音量 (0-100)
            pitch: 音调 (0-100)
            provider: TTS服务商，默认使用配置的默认服务商

        Returns:
            str: 音频文件的URL，失败返回None
        """
        provider = provider or self.default_provider
        
        try:
            if provider == 'xunfei':
                return self._synthesize_with_xunfei(text, voice, speed, emotion, volume, pitch)
            elif provider == 'aliyun':
                return self._synthesize_with_aliyun(text, voice, speed, emotion, volume, pitch)
            else:
                logger.error(f"Unsupported TTS provider: {provider}")
                return None

        except Exception as e:
            logger.error(f"TTS synthesis failed: {str(e)}")
            return None
    
    def _synthesize_with_xunfei(self, text: str, voice: str, speed: str, emotion: str, volume: int, pitch: int) -> Optional[str]:
        """
        使用讯飞TTS合成语音
        """
        if not all([self.xunfei_app_id, self.xunfei_api_key, self.xunfei_api_secret]):
            logger.error("讯飞TTS配置不完整")
            return None
        
        # 构建请求参数
        voice_name = self.voice_configs['xunfei']['voices'].get(voice, 'x2_xiaojuan')
        voice_speed = self.voice_configs['xunfei']['speeds'].get(speed, 50)
        
        # 讯飞TTS API调用逻辑
        # 这里需要根据讯飞官方文档实现具体的API调用
        # 包括签名生成、请求发送、响应处理等
        
        logger.info(f"使用讯飞TTS合成语音: {text[:50]}...")
        
        # TODO: 实现讯飞TTS API调用
        # 1. 生成签名
        # 2. 构建请求体
        # 3. 发送请求
        # 4. 处理响应
        # 5. 上传音频到OSS
        # 6. 返回音频URL
        
        # 临时返回模拟URL
        return "https://example.com/audio/temp.mp3"
    
    def _synthesize_with_aliyun(self, text: str, voice: str, speed: str, emotion: str, volume: int, pitch: int) -> Optional[str]:
        """
        使用阿里云TTS合成语音
        """
        if not all([self.aliyun_access_key_id, self.aliyun_access_key_secret]):
            logger.error("阿里云TTS配置不完整")
            return None
        
        logger.info(f"使用阿里云TTS合成语音: {text[:50]}...")
        
        # TODO: 实现阿里云TTS API调用
        # 临时返回模拟URL
        return "https://example.com/audio/temp.mp3"
    
    def get_supported_voices(self, provider: Optional[str] = None) -> Dict[str, Any]:
        """
        获取支持的语音列表
        """
        provider = provider or self.default_provider
        return self.voice_configs.get(provider, {})

    def get_voice_by_character_traits(self, gender: str, age: int, personality: str, provider: Optional[str] = None) -> str:
        """
        根据角色特征推荐合适的音色

        Args:
            gender: 性别 ('male', 'female', 'other')
            age: 年龄
            personality: 性格描述
            provider: TTS服务商

        Returns:
            str: 推荐的音色ID
        """
        provider = provider or self.default_provider
        voices = self.voice_configs.get(provider, {}).get('voices', {})

        # 根据年龄分组
        if age <= 12:
            age_group = 'child'
        elif age <= 25:
            age_group = 'young'
        elif age <= 50:
            age_group = 'adult'
        else:
            age_group = 'elderly'

        # 根据性格特征选择音色
        personality_lower = personality.lower()

        if gender.lower() == 'female':
            if age_group == 'child':
                return 'child_girl'
            elif age_group == 'elderly':
                return 'elderly_female'
            elif '可爱' in personality or 'cute' in personality_lower or '活泼' in personality:
                return 'female_cute'
            elif '温柔' in personality or 'gentle' in personality_lower or '优雅' in personality:
                return 'female_gentle'
            elif '成熟' in personality or 'mature' in personality_lower:
                return 'female_mature'
            elif '优雅' in personality or 'elegant' in personality_lower:
                return 'female_elegant'
            elif '活力' in personality or 'lively' in personality_lower:
                return 'female_lively'
            else:
                return 'female_sweet'  # 默认甜美女声

        elif gender.lower() == 'male':
            if age_group == 'child':
                return 'child_boy'
            elif age_group == 'elderly':
                return 'elderly_male'
            elif '成熟' in personality or 'mature' in personality_lower:
                return 'male_mature'
            elif '温和' in personality or 'gentle' in personality_lower:
                return 'male_gentle'
            elif '活力' in personality or 'energetic' in personality_lower or '活泼' in personality:
                return 'male_energetic'
            elif '深沉' in personality or 'deep' in personality_lower:
                return 'male_deep'
            elif age_group == 'young':
                return 'male_young'
            else:
                return 'male_warm'  # 默认温暖男声

        # 默认返回甜美女声
        return 'female_sweet'

    def get_emotion_by_context(self, text: str, character_personality: str) -> str:
        """
        根据文本内容和角色性格推荐情感

        Args:
            text: 要合成的文本
            character_personality: 角色性格

        Returns:
            str: 推荐的情感
        """
        text_lower = text.lower()
        personality_lower = character_personality.lower()

        # 检测文本中的情感关键词
        if any(word in text for word in ['哈哈', '开心', '高兴', '太好了', '棒', '！！']):
            return 'happy'
        elif any(word in text for word in ['难过', '伤心', '哭', '呜呜', '可惜']):
            return 'sad'
        elif any(word in text for word in ['生气', '愤怒', '讨厌', '烦', '气死了']):
            return 'angry'
        elif any(word in text for word in ['哇', '天哪', '不会吧', '真的吗', '？？']):
            return 'surprised'

        # 根据角色性格调整默认情感
        if '活泼' in personality_lower or '开朗' in personality_lower:
            return 'happy'
        elif '温柔' in personality_lower or '平静' in personality_lower:
            return 'neutral'

        return 'neutral'  # 默认中性情感

# 创建全局TTS服务实例
try:
    tts_service = TTSService()
    logger.info("TTS服务初始化成功")
except Exception as e:
    logger.error(f"TTS服务初始化失败: {str(e)}")
    tts_service = None
