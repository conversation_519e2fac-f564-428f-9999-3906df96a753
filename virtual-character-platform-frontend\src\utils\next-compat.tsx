// Next.js 兼容性模块 - 为React项目提供Next.js API的替代实现

import React, { lazy, type ComponentType, type ReactNode } from 'react';
import { Link as RouterLink } from 'react-router-dom';

// 默认导出 - 用于 next/dynamic
const dynamic = (
  importFunc: () => Promise<{ default: ComponentType<any> }>,
  options?: {
    loading?: () => ReactNode;
    ssr?: boolean;
  }
) => {
  return lazy(importFunc);
};

export default dynamic;

// 替代 next/image
export const NextImage = ({ 
  src, 
  alt, 
  width, 
  height, 
  unoptimized,
  ...props 
}: {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  unoptimized?: boolean;
  [key: string]: any;
}) => {
  return (
    <img 
      src={src} 
      alt={alt} 
      width={width} 
      height={height}
      style={{ objectFit: 'cover' }}
      {...props}
    />
  );
};

// 替代 next/link
export const Link = ({ 
  href, 
  children, 
  ...props 
}: {
  href: string;
  children: ReactNode;
  [key: string]: any;
}) => {
  return (
    <RouterLink to={href} {...props}>
      {children}
    </RouterLink>
  );
};

// 替代 next/navigation
export const useRouter = () => {
  return {
    push: (url: string) => {
      window.location.href = url;
    },
    replace: (url: string) => {
      window.location.replace(url);
    },
    back: () => {
      window.history.back();
    }
  };
};

export const useSearchParams = () => {
  const params = new URLSearchParams(window.location.search);
  return {
    get: (key: string) => params.get(key),
    has: (key: string) => params.has(key),
    toString: () => params.toString()
  };
};
