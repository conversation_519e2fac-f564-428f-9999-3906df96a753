/**
 * Features模块Hooks集成测试
 * 验证Features模块所需的Hooks是否正常工作
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook } from '../testing-library/react';

// Mock dependencies
vi.mock('@/utils/fetch');
vi.mock('@/utils/file');
vi.mock('@/utils/storage');
vi.mock('@/utils/platform');
vi.mock('pwa-install-handler');

describe('Features模块Hooks测试', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('useLoadModel Hook', () => {
    it('应该提供模型加载功能', async () => {
      const { useLoadModel } = await import('../useLoadModel');
      const { result } = renderHook(() => useLoadModel());

      expect(result.current).toHaveProperty('downloading');
      expect(result.current).toHaveProperty('percent');
      expect(result.current).toHaveProperty('fetchModelUrl');
      
      expect(typeof result.current.downloading).toBe('boolean');
      expect(typeof result.current.percent).toBe('number');
      expect(typeof result.current.fetchModelUrl).toBe('function');
    });

    it('初始状态应该正确', async () => {
      const { useLoadModel } = await import('../useLoadModel');
      const { result } = renderHook(() => useLoadModel());

      expect(result.current.downloading).toBe(false);
      expect(result.current.percent).toBe(0);
    });
  });

  describe('usePWAInstall Hook', () => {
    it('应该提供PWA安装功能', async () => {
      const { usePWAInstall } = await import('../usePWAInstall');
      
      // Mock usePlatform
      vi.doMock('../usePlatform', () => ({
        usePlatform: () => ({
          isSupportInstallPWA: true,
          isPWA: false,
        }),
      }));

      const { result } = renderHook(() => usePWAInstall());

      expect(result.current).toHaveProperty('canInstall');
      expect(result.current).toHaveProperty('install');
      
      expect(typeof result.current.canInstall).toBe('boolean');
      expect(typeof result.current.install).toBe('function');
    });
  });

  describe('usePlatform Hook', () => {
    it('应该提供平台检测功能', async () => {
      const { usePlatform } = await import('../usePlatform');
      const { result } = renderHook(() => usePlatform());

      // 验证返回的平台信息结构
      const expectedProperties = [
        'isApple',
        'isChrome', 
        'isChromium',
        'isEdge',
        'isIOS',
        'isMacOS',
        'isPWA',
        'isSafari',
        'isSonomaOrLaterSafari',
        'isSupportInstallPWA'
      ];

      expectedProperties.forEach(prop => {
        expect(result.current).toHaveProperty(prop);
        expect(typeof result.current[prop]).toBe('boolean');
      });
    });
  });

  describe('useChatListActionsBar Hook', () => {
    it('应该提供聊天操作栏配置', async () => {
      // Mock useTranslation
      vi.doMock('react-i18next', () => ({
        useTranslation: () => ({
          t: (key: string) => key,
        }),
      }));

      const { useChatListActionsBar } = await import('../useChatListActionsBar');
      const { result } = renderHook(() => useChatListActionsBar());

      // 验证返回的操作栏配置结构
      const expectedActions = [
        'copy',
        'del',
        'delAndRegenerate',
        'divider',
        'edit',
        'regenerate',
        'tts'
      ];

      expectedActions.forEach(action => {
        expect(result.current).toHaveProperty(action);
      });

      // 验证操作项结构
      expect(result.current.copy).toHaveProperty('icon');
      expect(result.current.copy).toHaveProperty('key');
      expect(result.current.copy).toHaveProperty('label');
      
      expect(result.current.divider).toHaveProperty('type');
      expect(result.current.divider.type).toBe('divider');
    });
  });

  describe('Hooks集成验证', () => {
    it('所有Features模块需要的Hooks都应该可用', async () => {
      // 验证所有Hooks都能正常导入
      const hooks = await Promise.all([
        import('../useLoadModel'),
        import('../usePWAInstall'),
        import('../usePlatform'),
        import('../useChatListActionsBar'),
      ]);

      expect(hooks[0]).toHaveProperty('useLoadModel');
      expect(hooks[1]).toHaveProperty('usePWAInstall');
      expect(hooks[2]).toHaveProperty('usePlatform');
      expect(hooks[3]).toHaveProperty('useChatListActionsBar');
    });

    it('Hooks应该与Features模块兼容', async () => {
      // 验证Hooks的返回类型与Features模块期望一致
      const { useLoadModel } = await import('../useLoadModel');
      const { usePWAInstall } = await import('../usePWAInstall');
      const { usePlatform } = await import('../usePlatform');
      const { useChatListActionsBar } = await import('../useChatListActionsBar');

      // Mock dependencies for PWA
      vi.doMock('../usePlatform', () => ({
        usePlatform: () => ({
          isSupportInstallPWA: true,
          isPWA: false,
        }),
      }));

      const loadModelResult = renderHook(() => useLoadModel());
      const pwaInstallResult = renderHook(() => usePWAInstall());
      const platformResult = renderHook(() => usePlatform());
      const actionsBarResult = renderHook(() => useChatListActionsBar());

      // 验证所有Hooks都能正常渲染
      expect(loadModelResult.result.current).toBeDefined();
      expect(pwaInstallResult.result.current).toBeDefined();
      expect(platformResult.result.current).toBeDefined();
      expect(actionsBarResult.result.current).toBeDefined();
    });
  });
});
