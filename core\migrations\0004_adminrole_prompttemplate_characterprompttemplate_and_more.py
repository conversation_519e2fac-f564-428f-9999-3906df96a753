# Generated by Django 5.2.1 on 2025-06-06 17:57

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0003_alter_character_identity_alter_character_personality'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AdminRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='PromptTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('type', models.CharField(choices=[('image', '图片生成'), ('chat', '对话'), ('system', '系统')], max_length=50)),
                ('category', models.CharField(blank=True, max_length=50, null=True)),
                ('content', models.TextField()),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('version', models.CharField(blank=True, max_length=20, null=True)),
                ('variables', models.JSONField(blank=True, default=dict, null=True)),
                ('examples', models.JSONField(blank=True, default=dict, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_prompts', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='CharacterPromptTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('custom_content', models.TextField(blank=True, null=True)),
                ('variables_values', models.JSONField(blank=True, default=dict, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('character', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='prompt_templates', to='core.character')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='characters', to='core.prompttemplate')),
            ],
        ),
        migrations.CreateModel(
            name='SystemConfig',
            fields=[
                ('key', models.CharField(max_length=100, primary_key=True, serialize=False)),
                ('value', models.TextField()),
                ('description', models.TextField(blank=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='UserAdminRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='users', to='core.adminrole')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='admin_roles', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='AdminOperationLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation_type', models.CharField(choices=[('create', '创建'), ('update', '更新'), ('delete', '删除'), ('login', '登录'), ('logout', '登出'), ('other', '其他')], max_length=50)),
                ('target_type', models.CharField(blank=True, choices=[('character', '角色'), ('prompt_template', '提示词模板'), ('user', '用户'), ('system_config', '系统配置'), ('other', '其他')], max_length=50, null=True)),
                ('target_id', models.IntegerField(blank=True, null=True)),
                ('details', models.JSONField(blank=True, default=dict, null=True)),
                ('ip_address', models.CharField(blank=True, max_length=50, null=True)),
                ('user_agent', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='admin_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['user'], name='core_admino_user_id_e55fbe_idx'), models.Index(fields=['operation_type'], name='core_admino_operati_3c74dc_idx'), models.Index(fields=['target_type'], name='core_admino_target__9f256f_idx'), models.Index(fields=['created_at'], name='core_admino_created_20d85e_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='prompttemplate',
            index=models.Index(fields=['type'], name='core_prompt_type_bc2370_idx'),
        ),
        migrations.AddIndex(
            model_name='prompttemplate',
            index=models.Index(fields=['category'], name='core_prompt_categor_929578_idx'),
        ),
        migrations.AddIndex(
            model_name='prompttemplate',
            index=models.Index(fields=['is_active'], name='core_prompt_is_acti_ded564_idx'),
        ),
        migrations.AddIndex(
            model_name='characterprompttemplate',
            index=models.Index(fields=['character'], name='core_charac_charact_26b1d2_idx'),
        ),
        migrations.AddIndex(
            model_name='characterprompttemplate',
            index=models.Index(fields=['template'], name='core_charac_templat_a7bf5f_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='characterprompttemplate',
            unique_together={('character', 'template')},
        ),
        migrations.AddIndex(
            model_name='useradminrole',
            index=models.Index(fields=['user'], name='core_userad_user_id_c43d14_idx'),
        ),
        migrations.AddIndex(
            model_name='useradminrole',
            index=models.Index(fields=['role'], name='core_userad_role_id_eda7cb_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='useradminrole',
            unique_together={('user', 'role')},
        ),
    ]
