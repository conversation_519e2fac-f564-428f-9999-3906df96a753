"""
管理员API序列化器。
"""
from rest_framework import serializers
from django.contrib.auth.models import User
from core.models import (
    AdminRole,
    UserAdminRole,
    Character,
    PromptTemplate,
    AdminOperationLog,
    SystemConfig,
    CharacterPromptTemplate,
    UserProfile,
)

class AdminRoleSerializer(serializers.ModelSerializer):
    """管理员角色序列化器"""
    
    class Meta:
        model = AdminRole
        fields = '__all__'

class UserAdminRoleSerializer(serializers.ModelSerializer):
    """用户-管理员角色关联序列化器"""
    
    role_name = serializers.CharField(source='role.name', read_only=True)
    
    class Meta:
        model = UserAdminRole
        fields = ['id', 'user', 'role', 'role_name', 'created_at']

class AdminUserSerializer(serializers.ModelSerializer):
    """管理员用户序列化器"""
    
    roles = UserAdminRoleSerializer(source='admin_roles', many=True, read_only=True)
    is_active = serializers.BooleanField(required=False)
    
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 
                 'is_active', 'date_joined', 'last_login', 'roles']
        read_only_fields = ['date_joined', 'last_login']

class AdminCharacterSerializer(serializers.ModelSerializer):
    """管理员角色序列化器"""
    
    username = serializers.CharField(source='user.username', read_only=True)
    
    class Meta:
        model = Character
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']

class AdminPromptTemplateSerializer(serializers.ModelSerializer):
    """管理员提示词模板序列化器"""
    
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = PromptTemplate
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']

class AdminCharacterPromptTemplateSerializer(serializers.ModelSerializer):
    """管理员角色-提示词模板关联序列化器"""
    
    template_name = serializers.CharField(source='template.name', read_only=True)
    character_name = serializers.CharField(source='character.name', read_only=True)
    
    class Meta:
        model = CharacterPromptTemplate
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']

class AdminOperationLogSerializer(serializers.ModelSerializer):
    """管理员操作日志序列化器"""
    
    username = serializers.CharField(source='user.username', read_only=True)
    
    class Meta:
        model = AdminOperationLog
        fields = '__all__'
        read_only_fields = ['created_at']

class AdminSystemConfigSerializer(serializers.ModelSerializer):
    """管理员系统配置序列化器"""
    
    updated_by_username = serializers.CharField(source='updated_by.username', read_only=True)
    
    class Meta:
        model = SystemConfig
        fields = '__all__'
        read_only_fields = ['updated_at']

class AdminDashboardSerializer(serializers.Serializer):
    """管理员仪表盘序列化器"""
    
    total_users = serializers.IntegerField()
    active_users = serializers.IntegerField()
    total_characters = serializers.IntegerField()
    public_characters = serializers.IntegerField()
    total_templates = serializers.IntegerField()
    active_templates = serializers.IntegerField()
    recent_logs = AdminOperationLogSerializer(many=True)
    recent_users = AdminUserSerializer(many=True)