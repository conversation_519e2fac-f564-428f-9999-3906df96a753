"""
提示词工程服务测试脚本

用于验证提示词生成功能的测试脚本
"""

import os
import sys
import json
from pathlib import Path

# 添加当前目录到sys.path
current_dir = Path(__file__).parent
if str(current_dir) not in sys.path:
    sys.path.append(str(current_dir.parent.parent))

from services.prompt_engineering.models import (
    CharacterBasicInfo,
    AppearanceParams,
    CharacterSettings
)
from services.prompt_engineering.service import PromptEngineeringService


def test_image_prompt_generation():
    """测试图像提示词生成"""
    # 创建服务实例
    service = PromptEngineeringService()
    
    print("=== 测试图像提示词生成 ===")
    
    # 创建基本角色信息
    character = CharacterBasicInfo(
        name="雪月",
        race="elf",
        anime_reference="fantasy world",
        age=18,
        identity="魔法师",
        personality=["温柔", "聪明", "神秘"]
    )
    
    # 创建外观参数
    appearance = AppearanceParams(
        hair_style="long",
        hair_color="silver",
        eye_color="purple",
        skin_tone="fair",
        outfit_style="maid outfit"
    )
    
    # 生成基本提示词
    result1 = service.generate_image_prompt(character)
    print("\n1. 基础提示词 (仅角色信息):")
    print(f"正面提示词: {result1.positive_prompt}")
    print(f"负面提示词: {result1.negative_prompt}")
    print(f"是否被截断: {result1.is_truncated}")
    
    # 生成带外观参数的提示词
    result2 = service.generate_image_prompt(character, appearance)
    print("\n2. 带外观参数的提示词:")
    print(f"正面提示词: {result2.positive_prompt}")
    print(f"负面提示词: {result2.negative_prompt}")
    print(f"是否被截断: {result2.is_truncated}")
    
    # 测试自然语言描述
    nl_description = "给她黑色长发，蓝色眼睛，穿着校服"
    result3 = service.generate_image_prompt(character, appearance, nl_description)
    print("\n3. 带自然语言描述的提示词:")
    print(f"自然语言描述: {nl_description}")
    print(f"正面提示词: {result3.positive_prompt}")
    print(f"负面提示词: {result3.negative_prompt}")
    print(f"是否被截断: {result3.is_truncated}")


def test_chat_prompt_generation():
    """测试对话提示词生成"""
    # 创建服务实例
    service = PromptEngineeringService()
    
    print("\n=== 测试对话提示词生成 ===")
    
    # 创建基本角色信息
    character = CharacterBasicInfo(
        name="雪月",
        race="elf",
        age=18,
        identity="魔法师学徒",
        personality=["tsundere", "聪明"]
    )
    
    # 创建角色设定
    settings = CharacterSettings(
        background_story="雪月是一位来自精灵森林的魔法师学徒，自小离开家乡前往人类王国学习魔法。她虽然表面冷漠，但内心重感情，只是不擅长表达。",
        hobbies=["读书", "收集魔法水晶", "观星"],
        dialogue_style="formal",
        taboos=["战争", "种族歧视"]
    )
    
    # 生成系统提示词
    result = service.generate_chat_system_prompt(character, settings)
    print("\n系统提示词:")
    print(result.system_prompt)
    
    # 测试聊天历史上下文
    chat_history = [
        {"role": "user", "content": "你好，雪月！"},
        {"role": "assistant", "content": "唔...你好。有什么事吗？"},
        {"role": "user", "content": "今天天气真好，要不要一起去魔法塔看书？"},
        {"role": "assistant", "content": "我...我才不是因为想和你一起看书才答应的！只是刚好我也要去魔法塔查阅资料罢了。"}
    ]
    
    selected_history, count = service.generate_chat_history_context(chat_history, max_tokens=300)
    print(f"\n选择了 {count} 条历史消息")
    for msg in selected_history:
        print(f"{msg['role']}: {msg['content']}")


if __name__ == "__main__":
    test_image_prompt_generation()
    test_chat_prompt_generation() 