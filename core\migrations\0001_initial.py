# Generated by Django 5.2.1 on 2025-06-04 11:20

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Character',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('age', models.IntegerField()),
                ('identity', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('personality', models.Char<PERSON>ield(max_length=255)),
                ('image_url', models.URLField(blank=True, max_length=500, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('public', models.<PERSON>olean<PERSON>ield(default=False)),
                ('appearance_params', models.J<PERSON><PERSON><PERSON>(blank=True, default=dict, null=True)),
                ('settings', models.<PERSON><PERSON><PERSON><PERSON>(blank=True, default=dict, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='characters', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['created_at'],
            },
        ),
    ]
