"""
管理员服务模块，提供管理员相关的功能。
"""
import logging
from django.contrib.auth.models import User
from django.contrib.auth import authenticate
from django.db.models import Q
from core.models import AdminRole, UserAdminRole, AdminOperationLog
from core.auth.services.token_service import TokenService

logger = logging.getLogger(__name__)

class AdminService:
    """管理员服务类"""
    
    @staticmethod
    def authenticate_admin(username, password):
        """
        验证管理员身份。
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            tuple: (user, roles) 如果验证成功，否则 (None, None)
        """
        try:
            # 使用Django的认证系统验证用户
            user = authenticate(username=username, password=password)
            
            if user is None:
                logger.warning(f"管理员认证失败: 用户名或密码错误 - {username}")
                return None, None
            
            # 检查用户是否有管理员角色
            admin_roles = UserAdminRole.objects.filter(user=user)
            if not admin_roles.exists():
                logger.warning(f"管理员认证失败: 用户不是管理员 - {username}")
                return None, None
            
            # 获取用户的所有管理员角色名称
            roles = [role.role.name for role in admin_roles]
            
            return user, roles
        except Exception as e:
            logger.error(f"管理员认证异常: {str(e)}")
            return None, None
    
    @staticmethod
    def login_admin(username, password):
        """
        管理员登录。
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            dict: 包含用户信息和Token的字典，如果登录失败则返回None
        """
        user, roles = AdminService.authenticate_admin(username, password)
        
        if user is None:
            return None
        
        try:
            # 生成管理员Token
            token = TokenService.generate_admin_token(user.id, user.username, roles)
            
            # 记录登录日志
            AdminOperationLog.objects.create(
                user=user,
                operation_type='login',
                target_type='user',
                target_id=user.id,
                details={'roles': roles}
            )
            
            return {
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'roles': roles
                },
                'token': token
            }
        except Exception as e:
            logger.error(f"管理员登录异常: {str(e)}")
            return None
    
    @staticmethod
    def get_admin_roles(user):
        """
        获取用户的管理员角色。
        
        Args:
            user: User对象
            
        Returns:
            list: 角色名称列表
        """
        try:
            admin_roles = UserAdminRole.objects.filter(user=user)
            return [role.role.name for role in admin_roles]
        except Exception as e:
            logger.error(f"获取管理员角色异常: {str(e)}")
            return []
    
    @staticmethod
    def is_admin(user):
        """
        检查用户是否为管理员。
        
        Args:
            user: User对象
            
        Returns:
            bool: 如果用户是管理员则返回True，否则返回False
        """
        return UserAdminRole.objects.filter(user=user).exists()
    
    @staticmethod
    def log_operation(user, operation_type, target_type=None, target_id=None, details=None, request=None):
        """
        记录管理员操作日志。
        
        Args:
            user: User对象
            operation_type: 操作类型
            target_type: 目标类型
            target_id: 目标ID
            details: 操作详情
            request: HttpRequest对象
            
        Returns:
            AdminOperationLog: 创建的日志对象
        """
        try:
            ip_address = None
            user_agent = None
            
            if request:
                # 获取客户端IP
                x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
                if x_forwarded_for:
                    ip_address = x_forwarded_for.split(',')[0].strip()
                else:
                    ip_address = request.META.get('REMOTE_ADDR')
                
                # 获取用户代理
                user_agent = request.META.get('HTTP_USER_AGENT')
            
            # 创建日志记录
            log = AdminOperationLog.objects.create(
                user=user,
                operation_type=operation_type,
                target_type=target_type,
                target_id=target_id,
                details=details or {},
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            return log
        except Exception as e:
            logger.error(f"记录管理员操作日志异常: {str(e)}")
            return None 