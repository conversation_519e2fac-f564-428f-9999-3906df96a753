import logging
from datetime import timedelta
from django.utils import timezone
from django.db.models import Q
from core.models import Character
from .file_storage_service import FileStorageService

logger = logging.getLogger('app')

class FileCleanupService:
    """
    文件清理服务，用于清理软删除后的角色图片。
    """
    
    def __init__(self):
        """初始化文件清理服务"""
        self.file_storage = FileStorageService()
        
    def cleanup_deleted_character_images(self, retention_days=30):
        """
        清理已软删除且超过保留期限的角色图片
        
        Args:
            retention_days: 保留天数，默认30天
            
        Returns:
            tuple: 清理成功数量, 清理失败数量
        """
        # 计算截止日期
        cutoff_date = timezone.now() - timedelta(days=retention_days)
        
        # 查询已软删除且超过保留期限的角色
        expired_characters = Character.objects.filter(
            is_deleted=True,
            deleted_at__lt=cutoff_date,
            image_url__isnull=False
        ).exclude(image_url='')
        
        success_count = 0
        failure_count = 0
        
        for character in expired_characters:
            try:
                if character and character.image_url:
                    character_id = getattr(character, 'id', 'unknown')
                    # 删除图片
                    result = self.file_storage.delete_file(character.image_url)
                    if result:
                        logger.info(f"已成功删除角色图片: {character_id}, URL: {character.image_url}")
                        success_count += 1
                    else:
                        logger.warning(f"删除角色图片失败: {character_id}, URL: {character.image_url}")
                        failure_count += 1
            except Exception as e:
                character_id = getattr(character, 'id', 'unknown') if character else 'unknown'
                character_url = getattr(character, 'image_url', 'unknown') if character else 'unknown'
                logger.error(f"删除角色图片出错: {character_id}, URL: {character_url}, 错误: {str(e)}")
                failure_count += 1
                
        return success_count, failure_count 