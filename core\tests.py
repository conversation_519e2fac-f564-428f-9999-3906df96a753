# type: ignore
"""
测试文件 - 包含所有核心功能的单元测试
由于Django模型的动态属性特性，使用type: ignore来避免类型检查警告
"""
from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from .models import Character, ChatMessage, VALID_PERSONALITIES, VALID_IDENTITIES
from .data_access import UserDataAccess, CharacterDataAccess, ChatMessageDataAccess
from .prompt_engineering.personality_prompts import (
    get_personality_image_prompts,
    get_personality_dialogue_prompt,
    apply_personality_to_image_prompt,
    apply_personality_to_dialogue_prompt
)
from .prompt_engineering.identity_prompts import (
    get_identity_image_prompts, get_identity_dialogue_prompt,
    apply_identity_to_image_prompt, apply_identity_to_dialogue_prompt,
    combine_personality_identity_image_prompts, combine_personality_identity_dialogue_prompts
)
from unittest import mock



class UserDataAccessTestCase(TestCase):
    """用户数据访问层测试"""
    
    def setUp(self):
        """测试前的准备工作"""
        # 创建测试用户
        self.test_user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpassword123"
        )
    
    def test_create_user(self):
        """测试创建用户功能"""
        # 使用数据访问层创建用户
        new_user = UserDataAccess.create_user(
            username="newuser",
            email="<EMAIL>",
            password="newuserPassword123"
        )
        
        # 验证用户创建成功
        self.assertIsNotNone(new_user)
        if new_user is not None:
            self.assertEqual(new_user.username, "newuser")
            self.assertEqual(new_user.email, "<EMAIL>")

            # 验证用户profile也被创建
            self.assertTrue(hasattr(new_user, 'profile'))
            if hasattr(new_user, 'profile'):
                self.assertFalse(new_user.profile.is_deleted)  # type: ignore
    
    def test_get_user_by_id(self):
        """测试通过ID获取用户"""
        # 获取已创建的用户
        user = UserDataAccess.get_user_by_id(self.test_user.id)  # type: ignore

        # 验证获取成功
        self.assertIsNotNone(user)
        if user is not None:
            self.assertEqual(user.username, "testuser")  # type: ignore
    
    def test_get_user_by_username(self):
        """测试通过用户名获取用户"""
        # 获取已创建的用户
        user = UserDataAccess.get_user_by_username("testuser")
        
        # 验证获取成功
        self.assertIsNotNone(user)
        if user is not None:
            self.assertEqual(user.id, self.test_user.id)  # type: ignore
    
    def test_get_user_by_email(self):
        """测试通过邮箱获取用户"""
        # 获取已创建的用户
        user = UserDataAccess.get_user_by_email("<EMAIL>")
        
        # 验证获取成功
        self.assertIsNotNone(user)
        if user is not None:
            self.assertEqual(user.id, self.test_user.id)  # type: ignore
    
    def test_update_user(self):
        """测试更新用户信息"""
        # 更新用户信息
        updated_user = UserDataAccess.update_user(
            self.test_user.id,  # type: ignore
            first_name="Test",
            last_name="User"
        )

        # 验证更新成功
        self.assertIsNotNone(updated_user)
        if updated_user is not None:
            self.assertEqual(updated_user.first_name, "Test")  # type: ignore
            self.assertEqual(updated_user.last_name, "User")  # type: ignore
    
    def test_delete_and_restore_user(self):
        """测试软删除和恢复用户"""
        # 软删除用户
        delete_result = UserDataAccess.delete_user(self.test_user.id)  # type: ignore

        # 验证删除成功
        self.assertTrue(delete_result)

        # 重新获取用户，应该返回None（因为已软删除）
        deleted_user = UserDataAccess.get_user_by_id(self.test_user.id)  # type: ignore
        self.assertIsNone(deleted_user)

        # 但实际上用户仍然存在于数据库中
        direct_user = User.objects.get(id=self.test_user.id)  # type: ignore
        self.assertTrue(direct_user.profile.is_deleted)  # type: ignore

        # 恢复用户
        restore_result = UserDataAccess.restore_user(self.test_user.id)  # type: ignore

        # 验证恢复成功
        self.assertTrue(restore_result)

        # 现在应该能够再次获取用户
        restored_user = UserDataAccess.get_user_by_id(self.test_user.id)  # type: ignore
        self.assertIsNotNone(restored_user)
        if restored_user is not None:
            self.assertFalse(restored_user.profile.is_deleted)  # type: ignore


class CharacterDataAccessTestCase(TestCase):
    """角色数据访问层测试"""
    
    def setUp(self):
        """测试前的准备工作"""
        # 创建测试用户
        self.test_user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpassword123"
        )
        
        # 创建测试角色
        self.test_character = Character.objects.create(
            user=self.test_user,
            name="Test Character",
            age=25,
            identity="Tester",
            personality="Friendly",
            public=True
        )
    
    def test_create_character(self):
        """测试创建角色功能"""
        # 使用数据访问层创建角色
        new_character = CharacterDataAccess.create_character(
            user_id=self.test_user.id,
            name="New Character",
            age=30,
            identity="Developer",
            personality="Creative",
            image_url="https://example.com/image.jpg",
            appearance_params={"hair_color": "black", "eye_color": "blue"},
            settings={"greeting": "Hello, world!"},
            public=True
        )
        
        # 验证角色创建成功
        self.assertIsNotNone(new_character)
        self.assertEqual(new_character.name, "New Character")
        self.assertEqual(new_character.age, 30)
        self.assertEqual(new_character.identity, "Developer")
        self.assertEqual(new_character.personality, "Creative")
        self.assertEqual(new_character.image_url, "https://example.com/image.jpg")
        self.assertEqual(new_character.appearance_params, {"hair_color": "black", "eye_color": "blue"})
        self.assertEqual(new_character.settings, {"greeting": "Hello, world!"})
        self.assertTrue(new_character.public)
        self.assertEqual(new_character.user.id, self.test_user.id)
    
    def test_get_character_by_id(self):
        """测试通过ID获取角色"""
        # 获取已创建的角色
        character = CharacterDataAccess.get_character_by_id(self.test_character.id)
        
        # 验证获取成功
        self.assertIsNotNone(character)
        self.assertEqual(character.name, "Test Character")
    
    def test_get_characters_by_user(self):
        """测试获取用户的所有角色"""
        # 再创建一个角色
        CharacterDataAccess.create_character(
            user_id=self.test_user.id,
            name="Another Character",
            age=40,
            identity="Manager",
            personality="Serious",
            public=False
        )
        
        # 获取用户的所有角色
        characters = CharacterDataAccess.get_characters_by_user(self.test_user.id)
        
        # 验证获取成功
        self.assertEqual(len(characters), 2)
        
        # 只获取公开角色
        public_characters = CharacterDataAccess.get_characters_by_user(
            self.test_user.id,
            include_private=False
        )
        
        # 验证只有一个公开角色
        self.assertEqual(len(public_characters), 1)
        self.assertEqual(public_characters[0].name, "Test Character")
    
    def test_get_public_community_characters(self):
        """测试获取社区公开角色"""
        # 获取社区公开角色
        result = CharacterDataAccess.get_public_community_characters()
        
        # 验证获取成功
        self.assertEqual(result['total'], 1)
        self.assertEqual(len(result['results']), 1)
        self.assertEqual(result['results'][0].name, "Test Character")
    
    def test_update_character(self):
        """测试更新角色信息"""
        # 更新角色信息
        updated_character = CharacterDataAccess.update_character(
            self.test_character.id,
            name="Updated Character",
            age=26,
            appearance_params={"hair_color": "brown"}
        )
        
        # 验证更新成功
        self.assertIsNotNone(updated_character)
        self.assertEqual(updated_character.name, "Updated Character")
        self.assertEqual(updated_character.age, 26)
        self.assertEqual(updated_character.appearance_params, {"hair_color": "brown"})
    
    def test_delete_and_restore_character(self):
        """测试软删除和恢复角色"""
        # 软删除角色
        delete_result = CharacterDataAccess.delete_character(self.test_character.id)
        
        # 验证删除成功
        self.assertTrue(delete_result)
        
        # 再次获取角色，应该返回None（因为已软删除）
        deleted_character = CharacterDataAccess.get_character_by_id(self.test_character.id)
        self.assertIsNone(deleted_character)
        
        # 但如果包含已删除的角色，应该能获取
        included_character = CharacterDataAccess.get_character_by_id(
            self.test_character.id,
            include_deleted=True
        )
        self.assertIsNotNone(included_character)
        self.assertTrue(included_character.is_deleted)
        
        # 恢复角色
        restore_result = CharacterDataAccess.restore_character(self.test_character.id)
        
        # 验证恢复成功
        self.assertTrue(restore_result)
        
        # 现在应该能够再次获取角色
        restored_character = CharacterDataAccess.get_character_by_id(self.test_character.id)
        self.assertIsNotNone(restored_character)
        self.assertFalse(restored_character.is_deleted)


class ChatMessageDataAccessTestCase(TestCase):
    """聊天消息数据访问层测试"""
    
    def setUp(self):
        """测试前的准备工作"""
        # 创建测试用户
        self.test_user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpassword123"
        )
        
        # 创建测试角色
        self.test_character = Character.objects.create(
            user=self.test_user,
            name="Test Character",
            age=25,
            identity="Tester",
            personality="Friendly"
        )
        
        # 创建一些测试消息
        self.user_message = ChatMessage.objects.create(
            character=self.test_character,
            user=self.test_user,
            sender_type=ChatMessage.SenderType.USER,
            content="Hello, character!"
        )
        
        self.character_message = ChatMessage.objects.create(
            character=self.test_character,
            user=self.test_user,
            sender_type=ChatMessage.SenderType.CHARACTER,
            content="Hello, user!"
        )
    
    def test_create_message(self):
        """测试创建消息功能"""
        # 使用数据访问层创建消息
        new_message = ChatMessageDataAccess.create_message(
            character_id=self.test_character.id,
            user_id=self.test_user.id,
            content="This is a new message",
            sender_type=ChatMessage.SenderType.USER
        )
        
        # 验证消息创建成功
        self.assertIsNotNone(new_message)
        self.assertEqual(new_message.content, "This is a new message")
        self.assertEqual(new_message.sender_type, ChatMessage.SenderType.USER)
        self.assertEqual(new_message.character.id, self.test_character.id)
        self.assertEqual(new_message.user.id, self.test_user.id)
    
    def test_get_message_by_id(self):
        """测试通过ID获取消息"""
        # 获取已创建的消息
        message = ChatMessageDataAccess.get_message_by_id(self.user_message.id)
        
        # 验证获取成功
        self.assertIsNotNone(message)
        self.assertEqual(message.content, "Hello, character!")
    
    def test_get_conversation(self):
        """测试获取对话记录"""
        # 再创建几条消息
        for i in range(3):
            ChatMessageDataAccess.create_message(
                character_id=self.test_character.id,
                user_id=self.test_user.id,
                content=f"Message {i}",
                sender_type=ChatMessage.SenderType.USER if i % 2 == 0 else ChatMessage.SenderType.CHARACTER
            )
        
        # 获取对话记录
        conversation = ChatMessageDataAccess.get_conversation(
            character_id=self.test_character.id,
            user_id=self.test_user.id
        )
        
        # 验证获取成功
        self.assertEqual(conversation['total'], 5)  # 2个初始消息 + 3个新消息
        self.assertEqual(len(conversation['results']), 5)
    
    def test_get_latest_conversation(self):
        """测试获取最新对话记录"""
        # 再创建几条消息
        for i in range(10):
            ChatMessageDataAccess.create_message(
                character_id=self.test_character.id,
                user_id=self.test_user.id,
                content=f"Message {i}",
                sender_type=ChatMessage.SenderType.USER if i % 2 == 0 else ChatMessage.SenderType.CHARACTER
            )
        
        # 获取最新5条对话记录
        latest_messages = ChatMessageDataAccess.get_latest_conversation(
            character_id=self.test_character.id,
            user_id=self.test_user.id,
            limit=5
        )
        
        # 验证获取成功
        self.assertEqual(len(latest_messages), 5)
        self.assertEqual(latest_messages[0].content, "Message 5")
        self.assertEqual(latest_messages[4].content, "Message 9")
    
    def test_delete_message(self):
        """测试删除消息"""
        # 删除消息
        delete_result = ChatMessageDataAccess.delete_message(self.user_message.id)
        
        # 验证删除成功
        self.assertTrue(delete_result)
        
        # 尝试获取已删除的消息，应该返回None
        deleted_message = ChatMessageDataAccess.get_message_by_id(self.user_message.id)
        self.assertIsNone(deleted_message)


class CharacterPersonalityIdentityTestCase(TestCase):
    """测试角色性格和身份验证功能"""
    
    def setUp(self):
        """测试前的准备工作"""
        # 创建测试用户
        self.test_user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpassword123"
        )
    
    def test_valid_personality(self):
        """测试有效的性格值"""
        # 遍历所有有效性格，确保可以创建角色
        for personality in VALID_PERSONALITIES:
            character = Character.objects.create(
                user=self.test_user,
                name=f"Character with {personality}",
                age=20,
                identity="高中生",  # 使用有效的身份
                personality=personality
            )
            self.assertEqual(character.personality, personality)
    
    def test_invalid_personality(self):
        """测试无效的性格值"""
        # 创建一个角色对象，但不保存到数据库
        character = Character(
            user=self.test_user,
            name="Character with invalid personality",
            age=20,
            identity="高中生",
            personality="无效的性格"
        )
        # 尝试验证，应该引发ValidationError
        with self.assertRaises(ValidationError):
            character.full_clean()
    
    def test_valid_identity(self):
        """测试有效的身份值"""
        # 遍历所有有效身份，确保可以创建角色
        for identity in VALID_IDENTITIES:
            character = Character.objects.create(
                user=self.test_user,
                name=f"Character with {identity}",
                age=20,
                personality="温柔",  # 使用有效的性格
                identity=identity
            )
            self.assertEqual(character.identity, identity)
    
    def test_invalid_identity(self):
        """测试无效的身份值"""
        # 创建一个角色对象，但不保存到数据库
        character = Character(
            user=self.test_user,
            name="Character with invalid identity",
            age=20,
            personality="温柔",
            identity="无效的身份"
        )
        # 尝试验证，应该引发ValidationError
        with self.assertRaises(ValidationError):
            character.full_clean()
    
    def test_serializer_validation(self):
        """测试序列化器的验证功能"""
        from .serializers import CharacterSaveSerializer
        
        # 测试有效数据
        valid_data = {
            'name': 'Test Character',
            'age': 20,
            'personality': '温柔',
            'identity': '高中生',
            'public': True
        }
        serializer = CharacterSaveSerializer(data=valid_data)
        self.assertTrue(serializer.is_valid())
        
        # 测试无效性格
        invalid_personality_data = valid_data.copy()
        invalid_personality_data['personality'] = '无效的性格'
        serializer = CharacterSaveSerializer(data=invalid_personality_data)
        self.assertFalse(serializer.is_valid())
        
        # 测试无效身份
        invalid_identity_data = valid_data.copy()
        invalid_identity_data['identity'] = '无效的身份'
        serializer = CharacterSaveSerializer(data=invalid_identity_data)
        self.assertFalse(serializer.is_valid())


class PersonalityIdentityAPITestCase(APITestCase):
    """测试性格和身份API端点"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.client = APIClient()
    
    def test_get_personalities(self):
        """测试获取所有有效的性格列表"""
        url = reverse('personality-list')
        print(f"Personalities URL: {url}")  # 打印URL
        response = self.client.get(url)
        
        # 验证响应状态码
        self.assertEqual(response.status_code, 200)
        
        # 验证响应内容
        data = response.json()
        self.assertIn('personalities', data)
        self.assertIn('total', data)
        
        # 验证返回的性格列表与预定义列表匹配
        self.assertEqual(data['total'], len(VALID_PERSONALITIES))
        returned_personalities = [p['name'] for p in data['personalities']]
        for personality in VALID_PERSONALITIES:
            self.assertIn(personality, returned_personalities)
    
    def test_get_identities(self):
        """测试获取所有有效的身份列表"""
        url = reverse('identity-list')
        print(f"Identities URL: {url}")  # 打印URL
        response = self.client.get(url)
        
        # 验证响应状态码
        self.assertEqual(response.status_code, 200)
        
        # 验证响应内容
        data = response.json()
        self.assertIn('identities', data)
        self.assertIn('total', data)
        
        # 验证返回的身份列表与预定义列表匹配
        self.assertEqual(data['total'], len(VALID_IDENTITIES))
        returned_identities = [i['name'] for i in data['identities']]
        for identity in VALID_IDENTITIES:
            self.assertIn(identity, returned_identities)


class PersonalityPromptTestCase(TestCase):
    """测试性格对提示词的影响"""
    
    def test_get_personality_image_prompts(self):
        """测试获取性格图片提示词"""
        # 测试有效性格
        for personality in VALID_PERSONALITIES:
            prompts = get_personality_image_prompts(personality)
            self.assertIsNotNone(prompts)
            self.assertIn('english', prompts)
            self.assertIn('chinese', prompts)
            self.assertTrue(len(prompts['english']) > 0)
            self.assertTrue(len(prompts['chinese']) > 0)
        
        # 测试无效性格
        with self.assertRaises(ValueError):
            get_personality_image_prompts("无效的性格")
    
    def test_get_personality_dialogue_prompt(self):
        """测试获取性格对话提示词"""
        # 测试有效性格
        for personality in VALID_PERSONALITIES:
            prompt = get_personality_dialogue_prompt(personality)
            self.assertIsNotNone(prompt)
            self.assertTrue(len(prompt) > 0)
        
        # 测试无效性格
        with self.assertRaises(ValueError):
            get_personality_dialogue_prompt("无效的性格")
    
    def test_apply_personality_to_image_prompt(self):
        """测试将性格应用到图片提示词"""
        base_prompt = "a beautiful anime girl with blue hair"
        
        # 测试不同性格对提示词的影响
        for personality in VALID_PERSONALITIES:
            final_prompt = apply_personality_to_image_prompt(base_prompt, personality)
            self.assertIsNotNone(final_prompt)
            self.assertIn(base_prompt, final_prompt)
            
            # 检查性格特定的提示词是否被包含
            personality_prompts = get_personality_image_prompts(personality)
            self.assertTrue(
                personality_prompts['english'] in final_prompt or 
                personality_prompts['chinese'] in final_prompt
            )
        
        # 测试中文基础提示词
        chinese_base_prompt = "一个美丽的蓝发动漫女孩"
        for personality in VALID_PERSONALITIES:
            final_prompt = apply_personality_to_image_prompt(chinese_base_prompt, personality)
            self.assertIsNotNone(final_prompt)
            self.assertIn(chinese_base_prompt, final_prompt)
        
        # 测试长度限制
        long_base_prompt = "a " + "very " * 200 + "long prompt"
        for personality in VALID_PERSONALITIES:
            final_prompt = apply_personality_to_image_prompt(long_base_prompt, personality)
            self.assertIsNotNone(final_prompt)
            self.assertLessEqual(len(final_prompt), 1000)
        
        # 测试无效性格
        with self.assertRaises(ValueError):
            apply_personality_to_image_prompt(base_prompt, "无效的性格")
    
    def test_apply_personality_to_dialogue_prompt(self):
        """测试将性格应用到对话提示词"""
        base_prompt = "你是一个虚拟角色，名叫小明，年龄18岁。"
        
        # 测试不同性格对提示词的影响
        for personality in VALID_PERSONALITIES:
            final_prompt = apply_personality_to_dialogue_prompt(base_prompt, personality)
            self.assertIsNotNone(final_prompt)
            self.assertIn(base_prompt, final_prompt)
            
            # 检查性格特定的提示词是否被包含
            personality_prompt = get_personality_dialogue_prompt(personality)
            self.assertIn(personality_prompt, final_prompt)
        
        # 测试无效性格
        with self.assertRaises(ValueError):
            apply_personality_to_dialogue_prompt(base_prompt, "无效的性格")


class CharacterGenerateWithPersonalityTestCase(APITestCase):
    """测试性格对角色生成API的影响"""
    
    def setUp(self):
        """测试前的准备工作"""
        self.client = APIClient()
        # 创建测试用户并登录
        self.test_user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpassword123"
        )
        self.client.force_authenticate(user=self.test_user)
        
        # 生成角色的API端点
        self.generate_url = '/api/characters/generate/'
        
        # 模拟requests.post方法，避免实际调用星火API
        self.patcher = mock.patch('core.views.requests.post')
        self.mock_post = self.patcher.start()
        
        # 设置模拟响应
        mock_response = mock.Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "header": {"code": 0, "message": "success"},
            "payload": {"image": "base64_image_data"}
        }
        self.mock_post.return_value = mock_response
    
    def tearDown(self):
        """测试后的清理工作"""
        self.patcher.stop()
    
    def test_character_generate_with_personality(self):
        """测试不同性格对生成提示词的影响"""
        
        # 测试基础参数
        base_data = {
            'race': 'elf',
            'age': 18
        }
        
        # 测试不同性格
        for personality in VALID_PERSONALITIES:
            # 添加性格参数
            data = base_data.copy()
            data['personality'] = personality
            
            # 发送请求
            response = self.client.post(self.generate_url, data, format='json')
            
            # 验证响应状态码
            self.assertEqual(response.status_code, 200)
            
            # 验证响应内容
            self.assertIn('generated_prompt', response.data)
            
            # 验证性格特定的提示词是否被包含在生成的提示词中
            personality_prompts = get_personality_image_prompts(personality)
            generated_prompt = response.data['generated_prompt']
            
            self.assertTrue(
                personality_prompts['english'] in generated_prompt or 
                personality_prompts['chinese'] in generated_prompt,
                f"性格'{personality}'的提示词未包含在生成的提示词中: {generated_prompt}"
            )
    
    def test_character_generate_with_invalid_personality(self):
        """测试无效性格对生成提示词的影响"""
        
        # 使用无效的性格
        data = {
            'race': 'elf',
            'age': 18,
            'personality': '无效的性格'
        }
        
        # 发送请求
        response = self.client.post(self.generate_url, data, format='json')
        
        # 验证响应状态码（应该仍然成功，但使用回退逻辑）
        self.assertEqual(response.status_code, 200)
        
        # 验证响应内容
        self.assertIn('generated_prompt', response.data)
        
        # 验证无效性格被直接添加到提示词中
        generated_prompt = response.data['generated_prompt']
        self.assertIn('无效的性格', generated_prompt)


class IdentityPromptsTests(TestCase):
    """测试身份对提示词的影响机制"""
    
    def test_get_identity_image_prompts(self):
        """测试获取身份图片提示词"""
        # 测试有效身份
        for identity in VALID_IDENTITIES:
            prompts = get_identity_image_prompts(identity)
            self.assertIsInstance(prompts, dict)
            self.assertIn('english', prompts)
            self.assertIn('chinese', prompts)
            self.assertTrue(len(prompts['english']) > 0)
            self.assertTrue(len(prompts['chinese']) > 0)
        
        # 测试无效身份
        with self.assertRaises(ValueError):
            get_identity_image_prompts("无效身份")
    
    def test_get_identity_dialogue_prompt(self):
        """测试获取身份对话提示词"""
        # 测试有效身份
        for identity in VALID_IDENTITIES:
            prompt = get_identity_dialogue_prompt(identity)
            self.assertIsInstance(prompt, str)
            self.assertTrue(len(prompt) > 0)
        
        # 测试无效身份
        with self.assertRaises(ValueError):
            get_identity_dialogue_prompt("无效身份")
    
    def test_apply_identity_to_image_prompt(self):
        """测试将身份应用到图片提示词"""
        base_prompt = "beautiful anime girl"
        
        # 测试有效身份
        for identity in VALID_IDENTITIES:
            result = apply_identity_to_image_prompt(base_prompt, identity)
            self.assertIsInstance(result, str)
            self.assertIn(base_prompt, result)
            identity_prompts = get_identity_image_prompts(identity)
            self.assertIn(identity_prompts['english'], result)
        
        # 测试无效身份
        with self.assertRaises(ValueError):
            apply_identity_to_image_prompt(base_prompt, "无效身份")
        
        # 测试中文基础提示词
        base_prompt_cn = "美丽的动漫女孩"
        for identity in VALID_IDENTITIES:
            result = apply_identity_to_image_prompt(base_prompt_cn, identity)
            self.assertIsInstance(result, str)
            self.assertIn(base_prompt_cn, result)
            identity_prompts = get_identity_image_prompts(identity)
            self.assertIn(identity_prompts['chinese'], result)
    
    def test_apply_identity_to_dialogue_prompt(self):
        """测试将身份应用到对话提示词"""
        base_prompt = "You are a virtual character talking to the user."
        
        # 测试有效身份
        for identity in VALID_IDENTITIES:
            result = apply_identity_to_dialogue_prompt(base_prompt, identity)
            self.assertIsInstance(result, str)
            self.assertIn(base_prompt, result)
            identity_prompt = get_identity_dialogue_prompt(identity)
            self.assertIn(identity_prompt, result)
        
        # 测试无效身份
        with self.assertRaises(ValueError):
            apply_identity_to_dialogue_prompt(base_prompt, "无效身份")
    
    def test_combine_personality_identity_image_prompts(self):
        """测试组合性格和身份的图片提示词"""
        base_prompt = "beautiful anime girl"
        personality = "傲娇"
        identity = "高中生"
        
        result = combine_personality_identity_image_prompts(base_prompt, personality, identity)
        self.assertIsInstance(result, str)
        self.assertIn(base_prompt, result)
        
        # 检查性格和身份的提示词是否都包含在结果中
        from core.prompt_engineering.personality_prompts import get_personality_image_prompts
        personality_prompts = get_personality_image_prompts(personality)
        identity_prompts = get_identity_image_prompts(identity)
        
        self.assertIn(personality_prompts['english'], result)
        self.assertIn(identity_prompts['english'], result)
        self.assertIn(personality_prompts['chinese'], result)
        self.assertIn(identity_prompts['chinese'], result)
    
    def test_combine_personality_identity_dialogue_prompts(self):
        """测试组合性格和身份的对话提示词"""
        base_prompt = "You are a virtual character talking to the user."
        personality = "傲娇"
        identity = "高中生"
        
        result = combine_personality_identity_dialogue_prompts(base_prompt, personality, identity)
        self.assertIsInstance(result, str)
        self.assertIn(base_prompt, result)
        
        # 检查性格和身份的提示词是否都包含在结果中
        from core.prompt_engineering.personality_prompts import get_personality_dialogue_prompt
        personality_prompt = get_personality_dialogue_prompt(personality)
        identity_prompt = get_identity_dialogue_prompt(identity)
        
        self.assertIn(personality_prompt, result)
        self.assertIn(identity_prompt, result)
