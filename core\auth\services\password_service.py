"""
密码哈希与验证服务。

提供密码哈希和验证功能，使用Django内置的密码哈希机制。
"""
from django.contrib.auth.hashers import make_password, check_password
import re
import logging

logger = logging.getLogger(__name__)

class PasswordService:
    """密码哈希和验证服务"""
    
    @staticmethod
    def hash_password(password):
        """
        对密码进行哈希处理。
        
        Args:
            password: 原始密码明文
            
        Returns:
            str: 哈希后的密码
        """
        try:
            return make_password(password)
        except Exception as e:
            logger.error(f"密码哈希失败: {str(e)}")
            raise ValueError("密码哈希处理失败")
    
    @staticmethod
    def verify_password(password, hashed_password):
        """
        验证密码是否匹配哈希值。
        
        Args:
            password: 原始密码明文
            hashed_password: 存储的密码哈希值
            
        Returns:
            bool: 密码是否匹配
        """
        try:
            return check_password(password, hashed_password)
        except Exception as e:
            logger.error(f"密码验证失败: {str(e)}")
            return False
    
    @staticmethod
    def check_password_strength(password):
        """
        检查密码强度。
        
        密码必须满足以下条件：
        - 至少8个字符
        - 至少包含一个数字
        - 至少包含一个小写字母
        - 至少包含一个大写字母
        - 至少包含一个特殊字符
        
        Args:
            password: 原始密码明文
            
        Returns:
            tuple: (是否通过检查, 错误信息)
        """
        errors = []
        
        # 检查密码长度
        if len(password) < 8:
            errors.append("密码长度必须至少为8个字符")
        
        # 检查密码是否包含数字
        if not re.search(r'\d', password):
            errors.append("密码必须包含至少一个数字")
        
        # 检查密码是否包含小写字母
        if not re.search(r'[a-z]', password):
            errors.append("密码必须包含至少一个小写字母")
        
        # 检查密码是否包含大写字母
        if not re.search(r'[A-Z]', password):
            errors.append("密码必须包含至少一个大写字母")
        
        # 检查密码是否包含特殊字符
        if not re.search(r'[\W_]', password):
            errors.append("密码必须包含至少一个特殊字符")
        
        # 如果没有错误，则返回True和空的错误信息
        if not errors:
            return (True, "")
        
        return (False, ", ".join(errors)) 