import uuid
import base64
import binascii
from typing import Optional
from .oss_client import OSSClient
from .spark_error_handler import handle_spark_api_error

class SparkImageRequestBuilder:
    """
    用于构建星火图片生成 API 请求体的构建器。
    """

    @staticmethod
    def build_image_request_body(
        prompt: str,
        width: int,
        height: int,
        app_id: str,
        negative_prompt: Optional[str] = None
    ) -> dict:
        """
        根据提供的参数构建星火图片生成 API 的 POST 请求体。

        Args:
            prompt (str): 图片生成提示词。
            width (int): 图片宽度。
            height (int): 图片高度。
            app_id (str): 星火应用的 APP_ID。
            negative_prompt (str, optional): 反向提示词。默认为 None。

        Returns:
            dict: 包含 header, parameter, payload 的 JSON 请求体。
        """
        # 提示词长度限制校验（星火 API 提示词上限为 1000 字符）
        if len(prompt) > 1000:
            prompt = prompt[:1000] # 截断提示词

        request_body = {
            "header": {
                "app_id": app_id,
                "uid": str(uuid.uuid4()) # 使用 UUID 作为用户ID
            },
            "parameter": {
                "chat": {
                    "domain": "image",
                    "width": width,
                    "height": height
                }
            },
            "payload": {
                "message": {
                    "text": [
                        {"role": "user", "content": prompt}
                    ]
                }
            }
        }

        if negative_prompt:
            if len(negative_prompt) > 1000:
                negative_prompt = negative_prompt[:1000] # 截断反向提示词
            request_body["payload"]["message"]["text"].append(
                {"role": "assistant", "content": negative_prompt} # 星火API将negative prompt放在assistant角色
            )
        
        return request_body

    @staticmethod
    def process_image_response(response_data: dict) -> str:
        """
        处理星火图片生成 API 的响应，提取图片并上传到 OSS。

        Args:
            response_data (dict): 星火 API 返回的 JSON 响应数据。

        Returns:
            str: 图片在云存储服务上的可访问 URL。

        Raises:
            ValueError: 如果 API 请求失败或响应数据格式不正确。
        """
        if not response_data or "header" not in response_data or "payload" not in response_data:
            raise ValueError("Invalid Spark API response format.")

        header = response_data["header"]
        payload = response_data["payload"]

        # 调用统一的错误处理函数
        handle_spark_api_error(header)

        try:
            # 提取 Base64 图片数据
            base64_image_data = payload["choices"][0]["text"][0]["content"]
            # 解码 Base64 数据
            image_bytes = base64.b64decode(base64_image_data)
        except (KeyError, IndexError, binascii.Error) as e:
            raise ValueError(f"Failed to decode image data from Spark API response: {e}")

        # 文件扩展名，根据实际情况判断，这里默认使用 .jpg
        # 实际项目中可能需要更智能地判断图片类型（如通过魔术数字或API返回的MIME类型）
        file_extension = ".jpg"

        try:
            oss_client = OSSClient()
            image_url = oss_client.upload_image(image_bytes, file_extension=file_extension)
            return image_url
        except Exception as e:
            raise ValueError(f"Failed to upload image to OSS: {e}") 