"""
提示词工程模块数据模型定义

此模块定义了提示词工程所需的输入和输出数据结构
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any


@dataclass
class CharacterBasicInfo:
    """角色基础信息，用于提示词生成的输入"""
    name: str
    race: Optional[str] = None  # 种族
    anime_reference: Optional[str] = None  # 动漫名字参考
    age: Optional[int] = None
    identity: Optional[str] = None  # 身份
    personality: List[str] = field(default_factory=list)  # 性格标签列表


@dataclass
class AppearanceParams:
    """角色外观参数，用于定制化生成"""
    hair_style: Optional[str] = None
    hair_color: Optional[str] = None
    eye_color: Optional[str] = None
    skin_tone: Optional[str] = None
    body_type: Optional[str] = None
    outfit_style: Optional[str] = None
    facial_expression: Optional[str] = None
    # 可以根据实际需求添加更多参数
    
    # 存储任意其他未预定义的参数
    additional_params: Dict[str, Any] = field(default_factory=dict)


@dataclass
class CharacterSettings:
    """角色高级设定，用于对话AI人格设定"""
    background_story: Optional[str] = None
    hobbies: List[str] = field(default_factory=list)
    dialogue_style: Optional[str] = None
    knowledge_base: List[str] = field(default_factory=list)  # 角色可能拥有的特定知识领域
    taboos: List[str] = field(default_factory=list)  # 对话禁忌


@dataclass
class ImagePromptResult:
    """图像生成提示词结果"""
    positive_prompt: str  # 正面提示词
    negative_prompt: str = ""  # 负面提示词
    is_truncated: bool = False  # 是否因长度限制被截断
    truncated_content: List[str] = field(default_factory=list)  # 被截断的内容


@dataclass
class ChatPromptResult:
    """对话AI提示词结果"""
    system_prompt: str  # 系统提示词
    history_included: bool = True  # 是否包含了完整历史记录
    history_messages_count: int = 0  # 包含的历史消息数量