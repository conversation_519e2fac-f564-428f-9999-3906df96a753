# Generated by Django 5.2.1 on 2025-07-04 10:31

import core.models
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0007_add_gender_field'),
    ]

    operations = [
        migrations.CreateModel(
            name='CharacterBackground',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('scene_type', models.CharField(max_length=100, validators=[core.models.validate_scene_type], verbose_name='场景类型')),
                ('scene_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='场景名称')),
                ('image_url', models.CharField(blank=True, max_length=500, null=True, verbose_name='背景图片URL')),
                ('generation_prompt', models.TextField(blank=True, null=True, verbose_name='生成提示词')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='生成时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('generation_status', models.CharField(choices=[('pending', '待生成'), ('generating', '生成中'), ('completed', '已完成'), ('failed', '生成失败')], default='pending', max_length=20, verbose_name='生成状态')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='错误信息')),
                ('character', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='backgrounds', to='core.character', verbose_name='关联角色')),
            ],
            options={
                'verbose_name': '角色背景图片',
                'verbose_name_plural': '角色背景图片',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['character'], name='core_charac_charact_bacd6f_idx'), models.Index(fields=['generation_status'], name='core_charac_generat_b3c5ab_idx'), models.Index(fields=['scene_type'], name='core_charac_scene_t_bb8362_idx'), models.Index(fields=['created_at'], name='core_charac_created_a255cd_idx')],
            },
        ),
    ]
