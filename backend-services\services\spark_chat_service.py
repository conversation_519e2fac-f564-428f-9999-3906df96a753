import base64
import datetime
import hashlib
import hmac
import json
from urllib.parse import urlparse
import ssl
from datetime import datetime
from time import mktime
from urllib.parse import urlencode
from wsgiref.handlers import format_date_time
import websocket
import threading
import os
import logging

logger = logging.getLogger(__name__)

class SparkChatService:
    """
    基于官方WebSocket示例的星火AI对话服务
    专门用于对话功能，与图片生成服务分离
    """
    
    def __init__(self):
        # 从环境变量获取API凭证
        app_id = os.getenv('SPARK_APP_ID')
        api_key = os.getenv('SPARK_API_KEY')
        api_secret = os.getenv('SPARK_API_SECRET')

        # 验证配置
        if not all([app_id, api_key, api_secret]):
            raise ValueError("Spark API credentials not found in environment variables")

        # 确保类型安全，这些属性不会为None
        self.app_id: str = app_id  # type: ignore
        self.api_key: str = api_key  # type: ignore
        self.api_secret: str = api_secret  # type: ignore

        # 星火对话API配置
        self.domain = "x1"  # 模型版本
        self.spark_url = "wss://spark-api.xf-yun.com/v1/x1"
    
    def create_url(self):
        """
        生成WebSocket连接URL（基于官方示例）
        """
        host = urlparse(self.spark_url).netloc
        path = urlparse(self.spark_url).path
        
        # 生成RFC1123格式的时间戳
        now = datetime.now()
        date = format_date_time(mktime(now.timetuple()))
        
        # 拼接字符串
        signature_origin = "host: " + host + "\n"
        signature_origin += "date: " + date + "\n"
        signature_origin += "GET " + path + " HTTP/1.1"
        
        # 进行hmac-sha256进行加密
        signature_sha = hmac.new(
            self.api_secret.encode('utf-8'), 
            signature_origin.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        
        signature_sha_base64 = base64.b64encode(signature_sha).decode(encoding='utf-8')
        
        authorization_origin = f'api_key="{self.api_key}", algorithm="hmac-sha256", headers="host date request-line", signature="{signature_sha_base64}"'
        
        authorization = base64.b64encode(authorization_origin.encode('utf-8')).decode(encoding='utf-8')
        
        # 将请求的鉴权参数组合为字典
        v = {
            "authorization": authorization,
            "date": date,
            "host": host
        }
        
        # 拼接鉴权参数，生成url
        url = self.spark_url + '?' + urlencode(v)
        return url
    
    def generate_params(self, messages):
        """
        生成请求参数（基于官方示例）
        
        Args:
            messages: 对话消息列表，格式为 [{"role": "user", "content": "消息内容"}]
        """
        data = {
            "header": {
                "app_id": self.app_id,
                "uid": "1234",
            },
            "parameter": {
                "chat": {
                    "domain": self.domain,
                    "temperature": 1.2,
                    "max_tokens": 32768
                }
            },
            "payload": {
                "message": {
                    "text": messages
                }
            }
        }
        return data
    
    def get_dialogue_response(self, character_prompt, user_message, chat_history=None):
        """
        获取AI对话响应
        
        Args:
            character_prompt: 角色系统提示词
            user_message: 用户消息
            chat_history: 聊天历史（可选）
            
        Returns:
            str: AI响应内容
        """
        try:
            # 构建消息列表
            messages = []
            
            # 添加系统提示词（角色设定）
            messages.append({
                "role": "system",
                "content": character_prompt
            })
            
            # 添加聊天历史（如果有）
            if chat_history:
                messages.extend(chat_history)
            
            # 添加当前用户消息
            messages.append({
                "role": "user",
                "content": user_message
            })
            
            # 检查消息长度，避免超过限制
            messages = self._check_message_length(messages)
            
            # 使用WebSocket获取响应
            response = self._send_websocket_request(messages)
            
            # 如果API调用失败，使用智能模拟响应
            if not response or "抱歉" in response:
                return self._generate_fallback_response(character_prompt, user_message)
            
            return response
            
        except Exception as e:
            logger.error(f"Spark dialogue service error: {str(e)}")
            # 使用智能模拟响应作为降级方案
            return self._generate_fallback_response(character_prompt, user_message)
    
    def _generate_fallback_response(self, character_prompt, user_message):
        """
        生成智能的降级响应（当API不可用时）
        """
        import re
        
        # 从角色提示词中提取角色信息
        name_match = re.search(r'你是([^，,。.]+)', character_prompt)
        character_name = name_match.group(1) if name_match else "我"
        
        # 根据用户消息类型生成不同的响应
        user_msg_lower = user_message.lower()
        
        if any(greeting in user_msg_lower for greeting in ['你好', 'hello', 'hi', '嗨']):
            return f"你好！我是{character_name}，很高兴认识你！有什么我可以帮助你的吗？"
        
        elif any(question in user_msg_lower for question in ['你是谁', '介绍', '自己']):
            personality_match = re.search(r'性格([^，,。.]+)', character_prompt)
            personality = personality_match.group(1) if personality_match else "友善"
            return f"我是{character_name}，一个{personality}的虚拟角色。我很乐意和你聊天交流！"
        
        elif any(question in user_msg_lower for question in ['怎么样', '如何', '什么']):
            return f"这是个很有趣的问题！作为{character_name}，我觉得我们可以一起探讨这个话题。你有什么具体的想法吗？"
        
        elif any(emotion in user_msg_lower for emotion in ['开心', '高兴', '快乐']):
            return f"听到你这么说我也很开心！{character_name}最喜欢看到大家开心的样子了。"
        
        elif any(emotion in user_msg_lower for emotion in ['难过', '伤心', '不开心']):
            return f"我能理解你的感受。作为{character_name}，我想告诉你，每个人都会有低落的时候，这很正常。有什么我可以帮你的吗？"
        
        else:
            # 通用响应
            responses = [
                f"作为{character_name}，我觉得你说得很有道理。能告诉我更多关于这个话题的想法吗？",
                f"这真是个有趣的观点！{character_name}很想听听你的更多想法。",
                f"我理解你的意思。{character_name}认为我们可以继续深入讨论这个话题。",
                f"你提到的这个问题很值得思考。作为{character_name}，我很乐意和你一起探讨。"
            ]
            import random
            return random.choice(responses)
    
    def _check_message_length(self, messages):
        """
        检查并限制消息长度（基于官方示例）
        """
        def get_length(messages):
            length = 0
            for message in messages:
                length += len(message["content"])
            return length
        
        # 限制在8K tokens以内
        while get_length(messages) > 8000 and len(messages) > 1:
            # 保留系统提示词，删除最早的对话
            if messages[0]["role"] == "system":
                if len(messages) > 2:
                    messages.pop(1)  # 删除系统提示词后的第一条消息
                else:
                    break
            else:
                messages.pop(0)
        
        return messages
    
    def _send_websocket_request(self, messages):
        """
        发送WebSocket请求并获取响应
        """
        response_content = ""
        error_occurred = False
        
        def on_message(ws, message):
            nonlocal response_content, error_occurred
            try:
                data = json.loads(message)
                code = data['header']['code']
                
                if code != 0:
                    logger.error(f'Spark API error: {code}, {data}')
                    error_occurred = True
                    ws.close()
                    return
                
                choices = data["payload"]["choices"]
                status = choices["status"]
                text = choices['text'][0]
                
                if 'content' in text and text['content']:
                    response_content += text["content"]
                
                if status == 2:  # 响应结束
                    ws.close()
                    
            except Exception as e:
                logger.error(f"WebSocket message processing error: {str(e)}")
                error_occurred = True
                ws.close()
        
        def on_error(ws, error):
            nonlocal error_occurred
            logger.error(f"WebSocket error: {error}")
            error_occurred = True

        def on_close(ws, close_status_code, close_msg):
            # WebSocket关闭回调，参数由websocket库要求但在此处不需要使用
            pass
        
        def on_open(ws):
            def run():
                try:
                    data = json.dumps(self.generate_params(messages))
                    ws.send(data)
                except Exception as e:
                    logger.error(f"WebSocket send error: {str(e)}")
                    ws.close()
            
            threading.Thread(target=run).start()
        
        try:
            # 创建WebSocket连接
            websocket.enableTrace(False)
            ws_url = self.create_url()
            ws = websocket.WebSocketApp(
                ws_url,
                on_message=on_message,
                on_error=on_error,
                on_close=on_close,
                on_open=on_open
            )
            
            # 运行WebSocket连接（阻塞直到完成）
            ws.run_forever(sslopt={"cert_reqs": ssl.CERT_NONE})
            
            if error_occurred:
                return "抱歉，服务暂时不可用，请稍后再试。"
            
            return response_content if response_content else "抱歉，我没有收到有效的响应。"
            
        except Exception as e:
            logger.error(f"WebSocket connection error: {str(e)}")
            return "抱歉，连接服务时出现问题，请稍后再试。"

# 创建全局服务实例
try:
    spark_chat_service = SparkChatService()
except Exception as e:
    logger.error(f"Failed to initialize Spark chat service: {str(e)}")
    spark_chat_service = None
