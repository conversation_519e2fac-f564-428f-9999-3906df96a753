"""
告警通知机制模块 - 为虚拟角色平台提供错误告警通知功能
"""
import logging
import time
import threading
import queue
import requests
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime, timedelta
from django.conf import settings


class AlertConfig:
    """告警配置类"""
    
    # 告警级别
    ALERT_LEVELS = ['ERROR', 'CRITICAL']
    
    # 告警频率限制
    MIN_ALERT_INTERVAL = 60  # 最小告警间隔（秒）
    MAX_ALERTS_PER_HOUR = 10  # 每小时最大告警次数
    
    # 告警通知渠道
    EMAIL_ENABLED = getattr(settings, 'ALERT_EMAIL_ENABLED', False)
    EMAIL_RECIPIENTS = getattr(settings, 'ALERT_EMAIL_RECIPIENTS', [])
    EMAIL_SENDER = getattr(settings, 'ALERT_EMAIL_SENDER', '')
    EMAIL_SMTP_SERVER = getattr(settings, 'ALERT_EMAIL_SMTP_SERVER', '')
    EMAIL_SMTP_PORT = getattr(settings, 'ALERT_EMAIL_SMTP_PORT', 587)
    EMAIL_SMTP_USERNAME = getattr(settings, 'ALERT_EMAIL_SMTP_USERNAME', '')
    EMAIL_SMTP_PASSWORD = getattr(settings, 'ALERT_EMAIL_SMTP_PASSWORD', '')
    
    WEBHOOK_ENABLED = getattr(settings, 'ALERT_WEBHOOK_ENABLED', False)
    WEBHOOK_URL = getattr(settings, 'ALERT_WEBHOOK_URL', '')
    WEBHOOK_TYPE = getattr(settings, 'ALERT_WEBHOOK_TYPE', 'generic')  # generic, dingtalk, wechat


class AlertManager:
    """告警管理器，用于管理和发送告警通知"""
    
    def __init__(self):
        """初始化告警管理器"""
        self.alert_history = {}  # 记录告警历史
        self.alert_queue = queue.Queue()  # 告警队列
        self.alert_thread = None  # 告警处理线程
        self.running = False  # 告警处理线程运行标志
        self.logger = logging.getLogger('app.alerts')
    
    def start(self):
        """启动告警处理线程"""
        if self.alert_thread is None or not self.alert_thread.is_alive():
            self.running = True
            self.alert_thread = threading.Thread(target=self._process_alerts)
            self.alert_thread.daemon = True
            self.alert_thread.start()
            self.logger.info("告警处理线程已启动")
    
    def stop(self):
        """停止告警处理线程"""
        self.running = False
        if self.alert_thread and self.alert_thread.is_alive():
            self.alert_thread.join(timeout=5.0)
            self.logger.info("告警处理线程已停止")
    
    def add_alert(self, level, message, details=None):
        """
        添加告警
        
        Args:
            level: 告警级别
            message: 告警消息
            details: 告警详情
        """
        # 检查告警级别
        if level not in AlertConfig.ALERT_LEVELS:
            return
        
        # 创建告警数据
        alert_data = {
            'timestamp': datetime.now().isoformat(),
            'level': level,
            'message': message,
            'details': details or {},
            'service': getattr(settings, 'SERVICE_NAME', 'virtual-character-platform'),
        }
        
        # 添加请求ID（如果有）
        if details and 'request_id' in details:
            alert_data['request_id'] = details['request_id']
        
        # 添加到告警队列
        self.alert_queue.put(alert_data)
        self.logger.debug(f"添加告警: {message}")
    
    def _process_alerts(self):
        """处理告警队列中的告警"""
        while self.running:
            try:
                # 从队列中获取告警
                try:
                    alert_data = self.alert_queue.get(timeout=1.0)
                except queue.Empty:
                    continue
                
                # 检查告警频率限制
                if not self._check_alert_rate_limit(alert_data):
                    self.logger.debug(f"告警被频率限制: {alert_data['message']}")
                    continue
                
                # 发送告警通知
                self._send_alert(alert_data)
                
                # 标记队列任务完成
                self.alert_queue.task_done()
            
            except Exception as e:
                self.logger.error(f"处理告警时发生异常: {str(e)}", exc_info=True)
                time.sleep(1.0)  # 避免异常情况下的高CPU占用
    
    def _check_alert_rate_limit(self, alert_data):
        """
        检查告警频率限制
        
        Args:
            alert_data: 告警数据
            
        Returns:
            是否允许发送告警
        """
        now = datetime.now()
        message_hash = hash(alert_data['message'])
        
        # 检查最小告警间隔
        if message_hash in self.alert_history:
            last_alert_time = self.alert_history[message_hash]['last_time']
            if now - last_alert_time < timedelta(seconds=AlertConfig.MIN_ALERT_INTERVAL):
                return False
        
        # 检查每小时最大告警次数
        hour_start = now.replace(minute=0, second=0, microsecond=0)
        hour_alerts = sum(
            1 for info in self.alert_history.values()
            if info['last_time'] >= hour_start
        )
        
        if hour_alerts >= AlertConfig.MAX_ALERTS_PER_HOUR:
            # 如果超过限制，只允许CRITICAL级别的告警
            return alert_data['level'] == 'CRITICAL'
        
        # 更新告警历史
        self.alert_history[message_hash] = {
            'last_time': now,
            'count': self.alert_history.get(message_hash, {}).get('count', 0) + 1
        }
        
        return True
    
    def _send_alert(self, alert_data):
        """
        发送告警通知
        
        Args:
            alert_data: 告警数据
        """
        # 记录告警日志
        self.logger.warning(f"发送告警通知: {alert_data['message']}")
        
        # 发送邮件告警
        if AlertConfig.EMAIL_ENABLED and AlertConfig.EMAIL_RECIPIENTS:
            self._send_email_alert(alert_data)
        
        # 发送Webhook告警
        if AlertConfig.WEBHOOK_ENABLED and AlertConfig.WEBHOOK_URL:
            self._send_webhook_alert(alert_data)
    
    def _send_email_alert(self, alert_data):
        """
        发送邮件告警
        
        Args:
            alert_data: 告警数据
        """
        try:
            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = AlertConfig.EMAIL_SENDER
            msg['To'] = ', '.join(AlertConfig.EMAIL_RECIPIENTS)
            msg['Subject'] = f"[{alert_data['level']}] {alert_data['service']} - {alert_data['message'][:50]}"
            
            # 构建邮件内容
            body = f"""
            <html>
            <body>
                <h2>告警通知</h2>
                <p><strong>服务:</strong> {alert_data['service']}</p>
                <p><strong>级别:</strong> {alert_data['level']}</p>
                <p><strong>时间:</strong> {alert_data['timestamp']}</p>
                <p><strong>消息:</strong> {alert_data['message']}</p>
            """
            
            # 添加请求ID
            if 'request_id' in alert_data:
                body += f"<p><strong>请求ID:</strong> {alert_data['request_id']}</p>"
            
            # 添加详情
            if alert_data['details']:
                body += "<h3>详情:</h3><pre>"
                for key, value in alert_data['details'].items():
                    body += f"{key}: {value}\n"
                body += "</pre>"
            
            body += """
            </body>
            </html>
            """
            
            msg.attach(MIMEText(body, 'html'))
            
            # 发送邮件
            server = smtplib.SMTP(AlertConfig.EMAIL_SMTP_SERVER, AlertConfig.EMAIL_SMTP_PORT)
            server.starttls()
            server.login(AlertConfig.EMAIL_SMTP_USERNAME, AlertConfig.EMAIL_SMTP_PASSWORD)
            server.send_message(msg)
            server.quit()
            
            self.logger.info(f"邮件告警已发送: {msg['Subject']}")
        
        except Exception as e:
            self.logger.error(f"发送邮件告警失败: {str(e)}", exc_info=True)
    
    def _send_webhook_alert(self, alert_data):
        """
        发送Webhook告警
        
        Args:
            alert_data: 告警数据
        """
        try:
            # 根据Webhook类型构建不同的请求数据
            if AlertConfig.WEBHOOK_TYPE == 'dingtalk':
                # 钉钉机器人消息格式
                request_data = {
                    "msgtype": "markdown",
                    "markdown": {
                        "title": f"[{alert_data['level']}] {alert_data['service']} - 告警",
                        "text": f"### [{alert_data['level']}] 告警通知\n" +
                               f"**服务:** {alert_data['service']}\n" +
                               f"**时间:** {alert_data['timestamp']}\n" +
                               f"**消息:** {alert_data['message']}\n" +
                               (f"**请求ID:** {alert_data['request_id']}\n" if 'request_id' in alert_data else "")
                    }
                }
            
            elif AlertConfig.WEBHOOK_TYPE == 'wechat':
                # 企业微信机器人消息格式
                request_data = {
                    "msgtype": "markdown",
                    "markdown": {
                        "content": f"### [{alert_data['level']}] 告警通知\n" +
                                  f">**服务:** {alert_data['service']}\n" +
                                  f">**时间:** {alert_data['timestamp']}\n" +
                                  f">**消息:** {alert_data['message']}\n" +
                                  (f">**请求ID:** {alert_data['request_id']}\n" if 'request_id' in alert_data else "")
                    }
                }
            
            else:
                # 通用Webhook格式
                request_data = alert_data
            
            # 发送Webhook请求
            response = requests.post(
                AlertConfig.WEBHOOK_URL,
                json=request_data,
                headers={'Content-Type': 'application/json'},
                timeout=5.0
            )
            
            # 检查响应
            if response.status_code >= 200 and response.status_code < 300:
                self.logger.info(f"Webhook告警已发送: {alert_data['message'][:50]}")
            else:
                self.logger.warning(f"Webhook告警发送失败，状态码: {response.status_code}, 响应: {response.text}")
        
        except Exception as e:
            self.logger.error(f"发送Webhook告警失败: {str(e)}", exc_info=True)


class AlertHandler(logging.Handler):
    """
    日志告警处理器，将ERROR和CRITICAL级别的日志转发为告警
    """
    def __init__(self, alert_manager):
        """
        初始化处理器
        
        Args:
            alert_manager: 告警管理器实例
        """
        super().__init__()
        self.alert_manager = alert_manager
        self.setLevel(logging.ERROR)
    
    def emit(self, record):
        """
        发出告警
        
        Args:
            record: 日志记录
        """
        try:
            # 获取日志消息
            message = self.format(record)
            
            # 获取详情
            details = {}
            extra = getattr(record, 'extra', None)
            if extra and isinstance(extra, dict):
                details.update(extra)

            # 添加异常信息
            if record.exc_info and len(record.exc_info) >= 2:
                exc_type = record.exc_info[0]
                exc_value = record.exc_info[1]
                if exc_type and hasattr(exc_type, '__name__'):
                    details['exception_type'] = exc_type.__name__
                if exc_value:
                    details['exception_message'] = str(exc_value)
            
            # 添加告警
            self.alert_manager.add_alert(record.levelname, message, details)
        
        except Exception:
            self.handleError(record)


# 创建全局告警管理器实例
alert_manager = AlertManager()


def configure_alert_system():
    """
    配置告警系统
    """
    # 启动告警管理器
    alert_manager.start()
    
    # 创建告警处理器
    alert_handler = AlertHandler(alert_manager)
    
    # 为根logger添加告警处理器
    root_logger = logging.getLogger()
    root_logger.addHandler(alert_handler)
    
    # 为app logger添加告警处理器
    app_logger = logging.getLogger('app')
    app_logger.addHandler(alert_handler)
    
    return alert_manager


def send_alert(level, message, details=None):
    """
    发送告警的便捷函数
    
    Args:
        level: 告警级别
        message: 告警消息
        details: 告警详情
    """
    alert_manager.add_alert(level, message, details) 