from django.shortcuts import render
from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.views import APIView
from django.contrib.auth import authenticate
from django.contrib.auth.models import User
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.views import TokenBlacklistView # For logout

from src.authentication.serializers import UserRegisterSerializer, UserLoginSerializer


class UserRegisterView(generics.CreateAPIView):
    queryset = User.objects.all()
    serializer_class = UserRegisterSerializer

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response({
            "message": "User registered successfully.",
            "user_id": serializer.instance.id,
            "username": serializer.instance.username,
            "email": serializer.instance.email
        }, status=status.HTTP_201_CREATED, headers=headers)


class UserLoginView(APIView):
    serializer_class = UserLoginSerializer

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        username_or_email = serializer.validated_data['username_or_email']
        password = serializer.validated_data['password']

        user = None
        if '@' in username_or_email:
            try:
                user = User.objects.get(email=username_or_email)
            except User.DoesNotExist:
                pass
        
        if user and user.check_password(password):
            # Authenticate the user if found by email
            authenticated_user = authenticate(request, username=user.username, password=password)
        else:
            # Try authenticating by username
            authenticated_user = authenticate(request, username=username_or_email, password=password)


        if authenticated_user is not None:
            refresh = RefreshToken.for_user(authenticated_user)
            access_token = getattr(refresh, 'access_token', None)
            return Response({
                'message': 'Login successful.',
                'access': str(access_token) if access_token else '',
                'refresh': str(refresh),
                'user_id': getattr(authenticated_user, 'id', None),
                'username': getattr(authenticated_user, 'username', ''),
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'detail': 'Invalid credentials.'
            }, status=status.HTTP_401_UNAUTHORIZED)


class UserLogoutView(TokenBlacklistView):
    permission_classes = (IsAuthenticated,)

    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)
        if response.status_code == status.HTTP_200_OK:
            original_detail = None
            if response.data and hasattr(response.data, 'get'):
                original_detail = response.data.get("detail")
            response.data = {"message": "Successfully logged out.", "detail": original_detail}
        return response
