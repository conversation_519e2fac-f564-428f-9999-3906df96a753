from django.contrib import admin
from django.contrib.auth.models import User
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import (
    UserProfile, Character, ChatMessage, AdminRole, UserAdminRole,
    PromptTemplate, CharacterPromptTemplate, SystemConfig, AdminOperationLog
)

# 扩展用户管理界面，显示用户Profile信息
class UserProfileInline(admin.StackedInline):
    model = UserProfile
    can_delete = False
    verbose_name_plural = '用户资料'
    fields = ('created_at', 'updated_at', 'is_deleted', 'deleted_at')
    readonly_fields = ('created_at', 'updated_at')

class UserAdmin(BaseUserAdmin):
    inlines = (UserProfileInline,)
    list_display = ('username', 'email', 'first_name', 'last_name', 'is_staff', 'is_active', 'date_joined')
    list_filter = ('is_staff', 'is_superuser', 'is_active', 'date_joined')

# 重新注册User模型
admin.site.unregister(User)
admin.site.register(User, UserAdmin)

# 用户Profile管理
@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'created_at', 'updated_at', 'is_deleted')
    list_filter = ('is_deleted', 'created_at', 'updated_at')
    search_fields = ('user__username', 'user__email')
    readonly_fields = ('created_at', 'updated_at')

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')

# 角色管理
@admin.register(Character)
class CharacterAdmin(admin.ModelAdmin):
    list_display = ('name', 'age', 'identity', 'personality', 'user', 'public', 'created_at', 'is_deleted')
    list_filter = ('identity', 'personality', 'public', 'is_deleted', 'created_at')
    search_fields = ('name', 'user__username', 'identity', 'personality')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'age', 'identity', 'personality', 'user', 'public')
        }),
        ('外观设置', {
            'fields': ('image_url', 'appearance_params'),
            'classes': ('collapse',)
        }),
        ('系统设置', {
            'fields': ('settings',),
            'classes': ('collapse',)
        }),
        ('状态信息', {
            'fields': ('created_at', 'updated_at', 'is_deleted', 'deleted_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')

# 聊天消息管理
@admin.register(ChatMessage)
class ChatMessageAdmin(admin.ModelAdmin):
    list_display = ('character', 'user', 'sender_type', 'content_preview', 'sent_at')
    list_filter = ('sender_type', 'sent_at')
    search_fields = ('character__name', 'user__username', 'content')
    readonly_fields = ('sent_at',)

    def content_preview(self, obj):
        return obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
    content_preview.short_description = '消息预览'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('character', 'user')

# 管理员角色管理
@admin.register(AdminRole)
class AdminRoleAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'created_at', 'updated_at')
    search_fields = ('name', 'description')
    readonly_fields = ('created_at', 'updated_at')

# 用户管理员角色关联管理
@admin.register(UserAdminRole)
class UserAdminRoleAdmin(admin.ModelAdmin):
    list_display = ('user', 'role', 'created_at')
    list_filter = ('role', 'created_at')
    search_fields = ('user__username', 'role__name')
    readonly_fields = ('created_at',)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'role')

# 提示词模板管理
@admin.register(PromptTemplate)
class PromptTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'type', 'category', 'is_active', 'version', 'created_by', 'created_at')
    list_filter = ('type', 'category', 'is_active', 'created_at')
    search_fields = ('name', 'description', 'content')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'type', 'category', 'description', 'is_active', 'version')
        }),
        ('内容', {
            'fields': ('content',),
        }),
        ('配置', {
            'fields': ('variables', 'examples'),
            'classes': ('collapse',)
        }),
        ('元信息', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('created_by')

# 角色提示词模板关联管理
@admin.register(CharacterPromptTemplate)
class CharacterPromptTemplateAdmin(admin.ModelAdmin):
    list_display = ('character', 'template', 'created_at', 'updated_at')
    list_filter = ('template__type', 'created_at')
    search_fields = ('character__name', 'template__name')
    readonly_fields = ('created_at', 'updated_at')

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('character', 'template')

# 系统配置管理
@admin.register(SystemConfig)
class SystemConfigAdmin(admin.ModelAdmin):
    list_display = ('key', 'description', 'updated_by', 'updated_at')
    search_fields = ('key', 'description', 'value')
    readonly_fields = ('updated_at',)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('updated_by')

# 管理员操作日志管理
@admin.register(AdminOperationLog)
class AdminOperationLogAdmin(admin.ModelAdmin):
    list_display = ('user', 'operation_type', 'target_type', 'target_id', 'ip_address', 'created_at')
    list_filter = ('operation_type', 'target_type', 'created_at')
    search_fields = ('user__username', 'details', 'ip_address')
    readonly_fields = ('created_at',)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')

    def has_add_permission(self, request):
        return False  # 不允许手动添加日志

    def has_change_permission(self, request, obj=None):
        return False  # 不允许修改日志

    def has_delete_permission(self, request, obj=None):
        return False  # 不允许删除日志

# 自定义管理员站点标题
admin.site.site_header = '虚拟角色平台管理后台'
admin.site.site_title = '虚拟角色平台'
admin.site.index_title = '管理后台首页'
