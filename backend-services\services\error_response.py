"""
统一错误响应格式模块 - 为虚拟角色平台提供标准化的API错误响应
"""
import os
import traceback
from django.conf import settings

# 全局错误码定义
# 1000-1999: 通用错误
# 2000-2999: 用户与认证相关错误
# 3000-3999: 角色相关错误
# 4000-4999: 交互相关错误
# 5000-5999: 外部服务相关错误
ERROR_CODES = {
    # 通用错误 (1000-1999)
    'UNKNOWN_ERROR': 1000,
    'INVALID_REQUEST': 1001,
    'INVALID_PARAMETERS': 1002,
    'RESOURCE_NOT_FOUND': 1003,
    'PERMISSION_DENIED': 1004,
    'SERVICE_UNAVAILABLE': 1005,
    
    # 用户与认证相关错误 (2000-2999)
    'AUTH_REQUIRED': 2000,
    'AUTH_FAILED': 2001,
    'TOKEN_EXPIRED': 2002,
    'USER_NOT_FOUND': 2003,
    'USER_ALREADY_EXISTS': 2004,
    
    # 角色相关错误 (3000-3999)
    'CHARACTER_NOT_FOUND': 3000,
    'CHARACTER_CREATE_FAILED': 3001,
    'CHARACTER_UPDATE_FAILED': 3002,
    'CHARACTER_DELETE_FAILED': 3003,
    
    # 交互相关错误 (4000-4999)
    'INTERACTION_FAILED': 4000,
    'RESPONSE_GENERATION_FAILED': 4001,
    
    # 外部服务相关错误 (5000-5999)
    'EXTERNAL_API_ERROR': 5000,
    'IMAGE_GENERATION_FAILED': 5001,
    'VOICE_GENERATION_FAILED': 5002,
}


def create_error_response(code_name, message, details=None):
    """
    创建标准错误响应
    
    Args:
        code_name: 错误代码名称，必须在ERROR_CODES中定义
        message: 用户友好的错误消息
        details: 可选的错误详情，通常只在开发环境下返回
        
    Returns:
        包含错误信息的字典
    """
    if code_name not in ERROR_CODES:
        code_name = 'UNKNOWN_ERROR'
    
    response = {
        'code': ERROR_CODES[code_name],
        'message': message,
    }
    
    # 只在非生产环境下添加详细错误信息
    debug_mode = getattr(settings, 'DEBUG', False)
    if debug_mode and details:
        response['details'] = details
    
    return response


def create_exception_response(exception, code_name='UNKNOWN_ERROR', message=None):
    """
    从异常创建标准错误响应
    
    Args:
        exception: 捕获的异常
        code_name: 错误代码名称，必须在ERROR_CODES中定义
        message: 用户友好的错误消息，如果为None则使用异常消息
        
    Returns:
        包含错误信息的字典
    """
    if message is None:
        message = str(exception)
    
    # 获取异常堆栈信息作为详情
    details = {
        'exception_type': exception.__class__.__name__,
        'stack_trace': traceback.format_exc(),
    }
    
    return create_error_response(code_name, message, details) 