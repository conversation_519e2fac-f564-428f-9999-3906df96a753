import hashlib
import hmac
import base64
import datetime
import json
from urllib.parse import urlparse

def generate_spark_signature(app_id: str, api_key: str, api_secret: str, request_url: str, request_body: dict):
    """
    Generates the authentication signature for Spark API requests.

    Args:
        app_id: The Spark API App ID.
        api_key: The Spark API Key.
        api_secret: The Spark API Secret.
        request_url: The full request URL.
        request_body: The JSON request body (payload).

    Returns:
        A dictionary containing the authentication headers.
    """
    # 1. Prepare common parameters
    current_time = datetime.datetime.now(datetime.timezone.utc)
    date = current_time.strftime("%a, %d %b %Y %H:%M:%S GMT")
    
    # Parse URL to get host and path
    url_parsed = urlparse(request_url)
    host = url_parsed.hostname
    path = url_parsed.path

    # 2. Build the signature string
    # Host header is derived from the request_url's hostname
    # Digest is SHA256 of the request body (payload)
    # The signature string follows the specific format required by Spark API
    
    # Note: Spark API requires the digest of the payload.
    # For now, we assume the request_body will be a dictionary that can be converted to JSON string.
    
    # Compute SHA256 digest of the request_body
    body_bytes = json.dumps(request_body, separators=(',', ':')).encode('utf-8')
    digest = 'SHA-256=' + base64.b64encode(hashlib.sha256(body_bytes).digest()).decode('utf-8')

    signature_origin = [
        f"host: {host}",
        f"date: {date}",
        f"POST {path} HTTP/1.1",
        f"digest: {digest}"
    ]
    signature_string = "\n".join(signature_origin)

    # 3. Sign the string with HMAC-SHA256
    hmac_key = api_secret.encode('utf-8')
    signature = hmac.new(hmac_key, signature_string.encode('utf-8'), hashlib.sha256).digest()
    
    # 4. Base64 encode the signature
    signature_b64 = base64.b64encode(signature).decode('utf-8')

    # 5. Construct the Authorization header
    authorization_header = (
        f'hmac username="{api_key}", algorithm="hmac-sha256", headers="host date request-line digest", signature="{signature_b64}"'
    )

    return {
        "Date": date,
        "Digest": digest,
        "Authorization": authorization_header
    } 