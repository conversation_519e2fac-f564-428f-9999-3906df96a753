"""
管理员API URL配置。
"""
from django.urls import path
from core.admin_api.views import (
    AdminRoleListView,
    AdminRoleDetailView,
    AdminUserListView,
    AdminUserDetailView,
    AdminCharacterListView,
    AdminCharacterDetailView,
    AdminPromptTemplateListView,
    AdminPromptTemplateDetailView,
    AdminOperationLogListView,
    AdminDashboardView,
    AdminSystemConfigView,
)

urlpatterns = [
    # 仪表盘
    path('dashboard', AdminDashboardView.as_view(), name='admin-dashboard'),
    
    # 角色管理
    path('characters', AdminCharacterListView.as_view(), name='admin-characters'),
    path('characters/<int:pk>', AdminCharacterDetailView.as_view(), name='admin-character-detail'),
    
    # 提示词模板管理
    path('prompt-templates', AdminPromptTemplateListView.as_view(), name='admin-prompt-templates'),
    path('prompt-templates/<int:pk>', AdminPromptTemplateDetailView.as_view(), name='admin-prompt-template-detail'),
    
    # 用户管理
    path('users', AdminUserListView.as_view(), name='admin-users'),
    path('users/<int:pk>', AdminUserDetailView.as_view(), name='admin-user-detail'),
    
    # 角色管理
    path('roles', AdminRoleListView.as_view(), name='admin-roles'),
    path('roles/<int:pk>', AdminRoleDetailView.as_view(), name='admin-role-detail'),
    
    # 系统配置
    path('config', AdminSystemConfigView.as_view(), name='admin-config'),
    
    # 操作日志
    path('logs', AdminOperationLogListView.as_view(), name='admin-logs'),
] 