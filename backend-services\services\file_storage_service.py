import os
import base64
from typing import Optional, Union
from .oss_client import OSSClient

class FileStorageService:
    """
    文件存储服务，封装OSS客户端操作，提供统一的文件存储接口。
    """
    
    def __init__(self):
        """初始化文件存储服务"""
        self.storage_client = OSSClient()
        
    def upload_image_data(self, image_data: bytes, user_id: Optional[int] = None,
                        character_id: Optional[int] = None, file_extension: str = ".jpg") -> str:
        """
        上传图片二进制数据到存储服务
        
        Args:
            image_data: 图片二进制数据
            user_id: 用户ID
            character_id: 角色ID
            file_extension: 文件扩展名，例如 ".jpg", ".png"
            
        Returns:
            str: 上传成功后的图片URL
        """
        return self.storage_client.upload_image(
            image_data=image_data,
            user_id=user_id,
            character_id=character_id,
            file_extension=file_extension
        )
    
    def upload_base64_image(self, base64_data: str, user_id: Optional[int] = None,
                           character_id: Optional[int] = None) -> str:
        """
        上传Base64编码的图片数据
        
        Args:
            base64_data: Base64编码的图片数据，可以包含或不包含前缀(data:image/jpeg;base64,)
            user_id: 用户ID
            character_id: 角色ID
            
        Returns:
            str: 上传成功后的图片URL
        """
        # 处理可能包含前缀的base64数据
        if ',' in base64_data:
            header, encoded = base64_data.split(',', 1)
            file_extension = self._get_extension_from_base64_header(header)
        else:
            encoded = base64_data
            file_extension = '.jpg'  # 默认扩展名
            
        # 解码Base64数据
        image_data = base64.b64decode(encoded)
        
        # 上传图片
        return self.upload_image_data(
            image_data=image_data,
            user_id=user_id,
            character_id=character_id,
            file_extension=file_extension
        )
    
    def _get_extension_from_base64_header(self, header: str) -> str:
        """从Base64头部获取文件扩展名"""
        if 'image/jpeg' in header:
            return '.jpg'
        elif 'image/png' in header:
            return '.png'
        elif 'image/gif' in header:
            return '.gif'
        elif 'image/webp' in header:
            return '.webp'
        else:
            return '.jpg'  # 默认扩展名
    
    def get_file_url(self, object_path: str) -> str:
        """
        获取文件的公开访问URL
        
        Args:
            object_path: 对象路径
            
        Returns:
            str: 文件的公开访问URL
        """
        return self.storage_client.get_public_url(object_path)
    
    def get_signed_url(self, object_path: str, expires: int = 3600) -> str:
        """
        获取带签名的临时访问URL
        
        Args:
            object_path: 对象路径
            expires: URL有效期（秒）
            
        Returns:
            str: 带签名的临时访问URL
        """
        return self.storage_client.get_signed_url(object_path, expires)
    
    def delete_file(self, url_or_path: str) -> bool:
        """
        删除文件
        
        Args:
            url_or_path: 文件URL或对象路径
            
        Returns:
            bool: 删除是否成功
        """
        # 判断参数是URL还是对象路径
        if url_or_path.startswith('http'):
            # 如果是URL，从URL中提取对象路径
            object_path = self.storage_client.extract_object_path_from_url(url_or_path)
        else:
            # 如果已经是对象路径
            object_path = url_or_path
            
        return self.storage_client.delete_file(object_path)
    
    def get_object_path_from_url(self, url: str) -> str:
        """
        从URL中提取对象路径
        
        Args:
            url: 文件URL
            
        Returns:
            str: 对象路径
        """
        return self.storage_client.extract_object_path_from_url(url) 