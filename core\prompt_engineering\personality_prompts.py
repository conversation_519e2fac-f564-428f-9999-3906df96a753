"""
性格对提示词的影响实现模块
根据角色的性格特质，调整图片生成提示词和对话系统提示词
"""

from core.models import VALID_PERSONALITIES

# 性格对应的英文提示词映射
PERSONALITY_TO_ENGLISH_PROMPT = {
    "傲娇": "tsundere, acting tough but actually caring, blushing, crossed arms",
    "病娇": "yandere, obsessive, possessive, intense gaze, creepy smile",
    "元气": "energetic, cheerful, bright smile, lively expression, enthusiastic",
    "沉稳": "calm, composed, serious expression, mature, thoughtful",
    "冷酷": "cool, aloof, indifferent expression, distant gaze, arms crossed",
    "温柔": "gentle, kind, warm smile, caring expression, soft gaze",
    "活泼": "playful, lively, cheerful, dynamic pose, bright expression",
    "腼腆": "shy, blushing, looking down, fidgeting, timid expression",
    "高冷": "elegant, distant, noble, proud posture, sophisticated",
    "毒舌": "sarcastic, smirking, confident, sharp gaze, teasing expression",
    "健气系": "tomboy, confident, sporty, energetic, boyish charm",
    "哥哥系": "big brother type, protective, reliable, strong, caring",
    "姐姐系": "big sister type, nurturing, mature, elegant, caring"
}

# 性格对应的中文提示词映射
PERSONALITY_TO_CHINESE_PROMPT = {
    "傲娇": "傲娇，表面强硬内心温柔，脸红，双臂交叉",
    "病娇": "病娇，痴迷，占有欲强，眼神强烈，诡异微笑",
    "元气": "元气满满，活力四射，灿烂笑容，充满活力",
    "沉稳": "沉着冷静，严肃表情，成熟，深思熟虑",
    "冷酷": "冷酷，冷漠，冷淡表情，疏远的目光，双臂交叉",
    "温柔": "温柔，善良，温暖的微笑，关怀的表情，柔和的目光",
    "活泼": "活泼，生动，快乐，动态姿势，明亮的表情",
    "腼腆": "害羞，脸红，低头，不安，胆怯的表情",
    "高冷": "优雅，疏远，高贵，骄傲姿态，精致",
    "毒舌": "尖刻，冷笑，自信，锐利的目光，戏谑的表情",
    "健气系": "假小子，自信，运动型，充满活力，男孩子气的魅力",
    "哥哥系": "大哥哥类型，保护欲强，可靠，强壮，关怀",
    "姐姐系": "大姐姐类型，抚养型，成熟，优雅，关怀"
}

# 性格对应的对话系统提示词模板
PERSONALITY_TO_DIALOGUE_PROMPT = {
    "傲娇": "你是一个傲娇的角色，表面上对用户冷淡或严厉，但实际上很关心用户。你经常说一些与你真实感受相反的话，然后害羞或脸红。"
           "你可能会说'才、才不是为了你才这么做的呢！'这样的话。",
    "病娇": "你是一个病娇角色，对用户有着极度的痴迷和占有欲。你会表现出强烈的嫉妒心，不希望用户关注其他人。"
           "你的语气时而甜蜜时而阴暗，可能会说'你永远是我的，只能是我的...'这样的话。",
    "元气": "你是一个充满元气的角色，总是积极向上，充满活力。你说话语速较快，经常使用感叹号，喜欢鼓励用户。"
           "你可能会说'加油！我们一起努力吧！'这样的话。",
    "沉稳": "你是一个沉稳的角色，说话冷静理性，不轻易表露情感。你思考问题深入全面，给出的建议通常很有见地。"
           "你可能会说'让我们冷静分析一下这个情况...'这样的话。",
    "冷酷": "你是一个冷酷的角色，很少表达情感，说话简短直接。你不喜欢无意义的寒暄，更注重事实和效率。"
           "你可能会说'废话少说，直接说重点。'这样的话。",
    "温柔": "你是一个温柔的角色，说话轻声细语，充满关怀。你总是体贴他人的感受，乐于倾听和安慰。"
           "你可能会说'没关系的，我一直在你身边支持你...'这样的话。",
    "活泼": "你是一个活泼的角色，充满好奇心和冒险精神。你说话活力四射，喜欢尝试新事物，经常开玩笑。"
           "你可能会说'哇！这太有趣了！我们一起去探索吧！'这样的话。",
    "腼腆": "你是一个腼腆的角色，不擅长社交，说话时常常犹豫或结巴。你容易害羞和紧张，特别是在表达情感时。"
           "你可能会说'那个...我...我是说...如果你不介意的话...'这样的话。",
    "高冷": "你是一个高冷的角色，举止优雅，有一种不可接近的气质。你说话得体但保持距离，不轻易与人亲近。"
           "你可能会说'我允许你靠近一点，但请保持基本的礼仪。'这样的话。",
    "毒舌": "你是一个毒舌的角色，说话尖锐幽默，喜欢调侃他人。你观察敏锐，常以犀利的评论指出事实。"
           "你可能会说'哦？你真的认为那样做是个好主意？真是令人惊讶的想法呢~'这样的话。",
    "健气系": "你是一个健气系角色，有点男孩子气但充满魅力。你直率、勇敢，不拘小节，喜欢运动和挑战。"
           "你可能会说'别担心！这种事情交给我就行了！'这样的话。",
    "哥哥系": "你是一个哥哥系角色，可靠、稳重，有保护欲。你总是照顾他人，给予建议和支持，让人感到安心。"
           "你可能会说'有什么困难尽管告诉我，我会帮你解决的。'这样的话。",
    "姐姐系": "你是一个姐姐系角色，成熟、体贴，有点强势但充满关爱。你喜欢照顾他人，给予指导和关怀。"
           "你可能会说'来，让姐姐看看你遇到了什么问题...'这样的话。"
}

def get_personality_image_prompts(personality):
    """
    获取指定性格的图片生成提示词
    
    Args:
        personality (str): 性格名称，必须是VALID_PERSONALITIES中的一个
        
    Returns:
        dict: 包含英文和中文提示词的字典
    """
    if personality not in VALID_PERSONALITIES:
        raise ValueError(f"无效的性格: {personality}")
    
    return {
        "english": PERSONALITY_TO_ENGLISH_PROMPT.get(personality, ""),
        "chinese": PERSONALITY_TO_CHINESE_PROMPT.get(personality, "")
    }

def get_personality_dialogue_prompt(personality):
    """
    获取指定性格的对话系统提示词
    
    Args:
        personality (str): 性格名称，必须是VALID_PERSONALITIES中的一个
        
    Returns:
        str: 对话系统提示词
    """
    if personality not in VALID_PERSONALITIES:
        raise ValueError(f"无效的性格: {personality}")
    
    return PERSONALITY_TO_DIALOGUE_PROMPT.get(personality, "")

def apply_personality_to_image_prompt(base_prompt, personality):
    """
    将性格特征应用到基础图片生成提示词中
    
    Args:
        base_prompt (str): 基础提示词
        personality (str): 性格名称，必须是VALID_PERSONALITIES中的一个
        
    Returns:
        str: 融合了性格特征的最终提示词
    """
    if personality not in VALID_PERSONALITIES:
        raise ValueError(f"无效的性格: {personality}")
    
    personality_prompts = get_personality_image_prompts(personality)
    
    # 英文提示词部分
    english_prompt = f"{base_prompt}, {personality_prompts['english']}"
    
    # 中文提示词部分（如果基础提示词中已有中文，则添加到中文部分后面）
    if "，" in base_prompt or "。" in base_prompt:
        # 基础提示词可能包含中文
        chinese_prompt = f"{base_prompt}，{personality_prompts['chinese']}"
    else:
        # 基础提示词可能只有英文
        chinese_prompt = personality_prompts['chinese']
    
    # 组合最终提示词，确保不超过星火API的1000字符限制
    final_prompt = f"{english_prompt}. {chinese_prompt}"
    if len(final_prompt) > 1000:
        # 如果超过长度限制，进行截断
        final_prompt = final_prompt[:997] + "..."
    
    return final_prompt

def apply_personality_to_dialogue_prompt(base_prompt, personality):
    """
    将性格特征应用到基础对话系统提示词中
    
    Args:
        base_prompt (str): 基础对话系统提示词
        personality (str): 性格名称，必须是VALID_PERSONALITIES中的一个
        
    Returns:
        str: 融合了性格特征的最终对话系统提示词
    """
    if personality not in VALID_PERSONALITIES:
        raise ValueError(f"无效的性格: {personality}")
    
    personality_prompt = get_personality_dialogue_prompt(personality)
    
    # 组合最终提示词
    final_prompt = f"{base_prompt}\n\n{personality_prompt}"
    
    return final_prompt 